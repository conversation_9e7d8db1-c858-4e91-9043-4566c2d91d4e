import 'package:flutter/material.dart';
import 'package:seqqo/core/utils/platform_utils.dart';
import 'package:seqqo/core/utils/responsive_utils.dart';

/// Interface de recherche dédiée pour mobile
class MobileSearchInterface extends StatefulWidget {
  final String hintText;
  final String? initialQuery;
  final ValueChanged<String> onQueryChanged;
  final VoidCallback? onClear;
  final List<String>? suggestions;
  final List<String>? recentSearches;
  final ValueChanged<String>? onSuggestionTap;

  const MobileSearchInterface({
    super.key,
    required this.hintText,
    this.initialQuery,
    required this.onQueryChanged,
    this.onClear,
    this.suggestions,
    this.recentSearches,
    this.onSuggestionTap,
  });

  @override
  State<MobileSearchInterface> createState() => _MobileSearchInterfaceState();
}

class _MobileSearchInterfaceState extends State<MobileSearchInterface> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialQuery ?? '');
    _focusNode = FocusNode();
    
    // Auto-focus sur mobile
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (ResponsiveUtils.isMobile(context)) {
        _focusNode.requestFocus();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _handleQueryChange(String query) {
    widget.onQueryChanged(query);
  }

  void _handleClear() {
    _controller.clear();
    widget.onQueryChanged('');
    widget.onClear?.call();
    if (ResponsiveUtils.isMobile(context)) {
      PlatformUtils.triggerHapticFeedback(HapticFeedbackType.light);
    }
  }

  void _handleSuggestionTap(String suggestion) {
    _controller.text = suggestion;
    widget.onQueryChanged(suggestion);
    widget.onSuggestionTap?.call(suggestion);
    if (ResponsiveUtils.isMobile(context)) {
      PlatformUtils.triggerHapticFeedback(HapticFeedbackType.light);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final mediaQuery = MediaQuery.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // Barre de recherche
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: ResponsiveUtils.getHorizontalPadding(context),
                vertical: 12,
              ),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                border: Border(
                  bottom: BorderSide(
                    color: theme.colorScheme.outline.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  // Bouton retour
                  IconButton(
                    onPressed: () {
                      if (ResponsiveUtils.isMobile(context)) {
                        PlatformUtils.triggerHapticFeedback(HapticFeedbackType.light);
                      }
                      Navigator.of(context).pop();
                    },
                    icon: Icon(
                      Icons.arrow_back,
                      color: theme.colorScheme.onSurface,
                    ),
                    tooltip: 'Retour',
                  ),
                  
                  // Champ de recherche
                  Expanded(
                    child: TextField(
                      controller: _controller,
                      focusNode: _focusNode,
                      decoration: InputDecoration(
                        hintText: widget.hintText,
                        hintStyle: TextStyle(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                          fontSize: 18,
                        ),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      style: const TextStyle(fontSize: 18),
                      textInputAction: TextInputAction.search,
                      onChanged: _handleQueryChange,
                      onSubmitted: (query) {
                        if (query.isNotEmpty) {
                          _handleQueryChange(query);
                        }
                      },
                    ),
                  ),
                  
                  // Bouton effacer
                  if (_controller.text.isNotEmpty)
                    IconButton(
                      onPressed: _handleClear,
                      icon: Icon(
                        Icons.clear,
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                      tooltip: 'Effacer',
                    ),
                ],
              ),
            ),
            
            // Contenu des suggestions
            Expanded(
              child: _buildSuggestions(context, theme),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSuggestions(BuildContext context, ThemeData theme) {
    final hasQuery = _controller.text.isNotEmpty;
    final suggestions = widget.suggestions ?? [];
    final recentSearches = widget.recentSearches ?? [];
    
    if (!hasQuery && recentSearches.isEmpty) {
      return _buildEmptyState(theme);
    }
    
    return ListView(
      padding: EdgeInsets.symmetric(
        horizontal: ResponsiveUtils.getHorizontalPadding(context),
        vertical: 8,
      ),
      children: [
        // Recherches récentes
        if (!hasQuery && recentSearches.isNotEmpty) ...[
          _buildSectionHeader(theme, 'Recherches récentes', Icons.history),
          const SizedBox(height: 8),
          ...recentSearches.map((search) => _buildSuggestionItem(
            theme, 
            search, 
            Icons.history,
            () => _handleSuggestionTap(search),
          )),
          const SizedBox(height: 16),
        ],
        
        // Suggestions
        if (hasQuery && suggestions.isNotEmpty) ...[
          _buildSectionHeader(theme, 'Suggestions', Icons.search),
          const SizedBox(height: 8),
          ...suggestions.map((suggestion) => _buildSuggestionItem(
            theme, 
            suggestion, 
            Icons.search,
            () => _handleSuggestionTap(suggestion),
          )),
        ],
      ],
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search,
            size: 64,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'Commencez à taper pour rechercher',
            style: TextStyle(
              fontSize: 16,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(ThemeData theme, String title, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildSuggestionItem(
    ThemeData theme, 
    String text, 
    IconData icon, 
    VoidCallback onTap,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              Icon(
                icon,
                size: 18,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  text,
                  style: const TextStyle(fontSize: 16),
                ),
              ),
              Icon(
                Icons.north_west,
                size: 16,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
