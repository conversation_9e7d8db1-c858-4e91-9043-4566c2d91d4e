import 'package:flutter/material.dart';

/// Classe utilitaire pour la gestion de la responsivité dans l'application
///
/// Cette classe fournit une source unique de vérité pour tous les breakpoints
/// et les utilitaires liés à la responsivité.
class ResponsiveUtils {
  // Breakpoints principaux
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;
  static const double largeDesktopBreakpoint = 1600;

  // Dimensions de contenu
  static const double maxContentWidth = 1400;
  static const double sidebarWidth = 250;
  static const double compactSidebarWidth = 80;

  // Dimensions tactiles
  static const double minTouchTargetSize =
      48.0; // Taille minimale recommandée pour les cibles tactiles
  static const double minTouchTargetSpacing =
      8.0; // Espacement minimal entre les cibles tactiles

  // Facteurs d'échelle pour la typographie
  static const double mobileFontScale = 1.0;
  static const double tabletFontScale = 1.05;
  static const double desktopFontScale = 1.1;

  // Padding adaptatif
  static const double smallPadding = 8.0;
  static const double defaultPadding = 16.0;
  static const double largePadding = 24.0;

  /// Détermine si l'écran est de taille mobile
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  /// Détermine si l'écran est de taille tablette
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < desktopBreakpoint;
  }

  /// Détermine si l'écran est de taille desktop
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktopBreakpoint;
  }

  /// Détermine si l'écran est de grande taille desktop
  static bool isLargeDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= largeDesktopBreakpoint;
  }

  /// Retourne le facteur d'échelle de police approprié en fonction de la taille de l'écran
  static double getFontScaleFactor(BuildContext context) {
    if (isDesktop(context)) return desktopFontScale;
    if (isTablet(context)) return tabletFontScale;
    return mobileFontScale;
  }

  /// Retourne le padding approprié en fonction de la taille de l'écran
  static EdgeInsets getScreenPadding(BuildContext context) {
    if (isMobile(context)) {
      return const EdgeInsets.all(smallPadding);
    } else if (isTablet(context)) {
      return const EdgeInsets.all(defaultPadding);
    } else {
      return const EdgeInsets.all(largePadding);
    }
  }

  /// Retourne le nombre de colonnes approprié pour une grille en fonction de la taille de l'écran
  static int getGridColumnCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < mobileBreakpoint) return 1;
    if (width < tabletBreakpoint) return 2;
    if (width < desktopBreakpoint) return 3;
    return 4;
  }

  /// Retourne la taille de bouton appropriée en fonction de la taille de l'écran
  static Size getButtonSize(BuildContext context, {bool isSmall = false}) {
    if (isMobile(context)) {
      return isSmall
          ? const Size(minTouchTargetSize, minTouchTargetSize)
          : const Size(minTouchTargetSize * 2, minTouchTargetSize);
    } else {
      return isSmall
          ? const Size(minTouchTargetSize, minTouchTargetSize)
          : const Size(minTouchTargetSize * 3, minTouchTargetSize);
    }
  }

  /// Retourne le padding de bouton approprié en fonction de la taille de l'écran
  static EdgeInsets getButtonPadding(BuildContext context,
      {bool isSmall = false}) {
    if (isMobile(context)) {
      return isSmall
          ? const EdgeInsets.symmetric(horizontal: 12, vertical: 8)
          : const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
    } else {
      return isSmall
          ? const EdgeInsets.symmetric(horizontal: 16, vertical: 8)
          : const EdgeInsets.symmetric(horizontal: 24, vertical: 12);
    }
  }

  /// Retourne la taille d'icône appropriée en fonction de la taille de l'écran
  static double getIconSize(BuildContext context, {bool isSmall = false}) {
    if (isMobile(context)) {
      return isSmall ? 16.0 : 20.0;
    } else {
      return isSmall ? 18.0 : 24.0;
    }
  }

  /// Retourne le nombre de colonnes approprié pour une grille responsive
  static int getGridColumns(BuildContext context,
      {int? mobileColumns, int? tabletColumns, int? desktopColumns}) {
    if (isMobile(context)) {
      return mobileColumns ?? 1;
    } else if (isTablet(context)) {
      return tabletColumns ?? 2;
    } else {
      return desktopColumns ?? 3;
    }
  }

  /// Retourne la largeur maximale du contenu en fonction de la taille de l'écran
  static double getMaxContentWidth(BuildContext context) {
    if (isMobile(context)) {
      return MediaQuery.of(context).size.width;
    } else if (isTablet(context)) {
      return 800;
    } else {
      return maxContentWidth;
    }
  }

  /// Retourne l'espacement approprié entre les éléments
  static double getSpacing(BuildContext context, {bool isLarge = false}) {
    if (isMobile(context)) {
      return isLarge ? 16.0 : 8.0;
    } else if (isTablet(context)) {
      return isLarge ? 20.0 : 12.0;
    } else {
      return isLarge ? 24.0 : 16.0;
    }
  }

  /// Retourne la hauteur d'un élément de liste appropriée
  static double getListItemHeight(BuildContext context,
      {bool isCompact = false}) {
    if (isMobile(context)) {
      return isCompact ? 48.0 : 64.0;
    } else {
      return isCompact ? 40.0 : 56.0;
    }
  }

  /// Retourne le padding horizontal approprié pour les écrans
  static double getHorizontalPadding(BuildContext context) {
    if (isMobile(context)) {
      return 16.0;
    } else if (isTablet(context)) {
      return 24.0;
    } else {
      return 32.0;
    }
  }

  /// Retourne le padding vertical approprié pour les écrans
  static double getVerticalPadding(BuildContext context) {
    if (isMobile(context)) {
      return 16.0;
    } else if (isTablet(context)) {
      return 20.0;
    } else {
      return 24.0;
    }
  }

  /// Détermine si l'orientation est portrait
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  /// Détermine si l'orientation est paysage
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// Retourne la taille d'écran actuelle sous forme d'énumération
  static ScreenSize getScreenSize(BuildContext context) {
    if (isMobile(context)) {
      return ScreenSize.mobile;
    } else if (isTablet(context)) {
      return ScreenSize.tablet;
    } else if (isLargeDesktop(context)) {
      return ScreenSize.largeDesktop;
    } else {
      return ScreenSize.desktop;
    }
  }

  /// Retourne true si l'écran est considéré comme "petit" (mobile en portrait ou tablette en portrait)
  static bool isSmallScreen(BuildContext context) {
    return isMobile(context) || (isTablet(context) && isPortrait(context));
  }

  /// Retourne le breakpoint approprié pour les media queries CSS
  static String getCSSBreakpoint(BuildContext context) {
    if (isMobile(context)) {
      return 'mobile';
    } else if (isTablet(context)) {
      return 'tablet';
    } else if (isLargeDesktop(context)) {
      return 'large-desktop';
    } else {
      return 'desktop';
    }
  }
}

/// Énumération des tailles d'écran
enum ScreenSize {
  mobile,
  tablet,
  desktop,
  largeDesktop,
}
