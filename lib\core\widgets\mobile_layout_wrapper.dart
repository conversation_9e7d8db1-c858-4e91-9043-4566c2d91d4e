import 'package:flutter/material.dart';
import '../utils/platform_utils.dart';
import '../utils/responsive_utils.dart';

/// Widget wrapper qui adapte automatiquement le layout pour mobile
class MobileLayoutWrapper extends StatelessWidget {
  /// Widget à afficher sur mobile
  final Widget mobileChild;
  
  /// Widget à afficher sur desktop (optionnel, utilise mobileChild par défaut)
  final Widget? desktopChild;
  
  /// Padding spécifique pour mobile
  final EdgeInsets? mobilePadding;
  
  /// Padding spécifique pour desktop
  final EdgeInsets? desktopPadding;
  
  /// Force l'utilisation du layout mobile même sur desktop
  final bool forceMobileLayout;
  
  /// Couleur de fond spécifique pour mobile
  final Color? mobileBackgroundColor;
  
  /// Couleur de fond spécifique pour desktop
  final Color? desktopBackgroundColor;

  const MobileLayoutWrapper({
    super.key,
    required this.mobileChild,
    this.desktopChild,
    this.mobilePadding,
    this.desktopPadding,
    this.forceMobileLayout = false,
    this.mobileBackgroundColor,
    this.desktopBackgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final shouldUseMobile = forceMobileLayout || 
        PlatformUtils.shouldUseMobileOptimizations(context);
    
    if (shouldUseMobile) {
      return _buildMobileLayout(context);
    } else {
      return _buildDesktopLayout(context);
    }
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Container(
      color: mobileBackgroundColor,
      padding: mobilePadding ?? ResponsiveUtils.getScreenPadding(context),
      child: mobileChild,
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Container(
      color: desktopBackgroundColor,
      padding: desktopPadding ?? ResponsiveUtils.getScreenPadding(context),
      child: desktopChild ?? mobileChild,
    );
  }
}

/// Widget qui adapte automatiquement les listes pour mobile
class MobileAdaptiveList extends StatelessWidget {
  /// Liste des widgets enfants
  final List<Widget> children;
  
  /// Espacement entre les éléments
  final double spacing;
  
  /// Padding de la liste
  final EdgeInsets? padding;
  
  /// Si la liste doit être scrollable
  final bool scrollable;
  
  /// Contrôleur de scroll
  final ScrollController? controller;
  
  /// Physics de scroll
  final ScrollPhysics? physics;

  const MobileAdaptiveList({
    super.key,
    required this.children,
    this.spacing = 8.0,
    this.padding,
    this.scrollable = true,
    this.controller,
    this.physics,
  });

  @override
  Widget build(BuildContext context) {
    final isMobile = PlatformUtils.shouldUseMobileOptimizations(context);
    
    // Ajuster l'espacement pour mobile
    final adaptiveSpacing = isMobile ? spacing + 4.0 : spacing;
    
    // Ajuster le padding pour mobile
    final adaptivePadding = padding ?? 
        (isMobile 
            ? const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0)
            : const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0));

    if (scrollable) {
      return ListView.separated(
        controller: controller,
        physics: physics ?? PlatformUtils.getScrollPhysics(),
        padding: adaptivePadding,
        itemCount: children.length,
        separatorBuilder: (context, index) => SizedBox(height: adaptiveSpacing),
        itemBuilder: (context, index) => children[index],
      );
    } else {
      return Padding(
        padding: adaptivePadding,
        child: Column(
          children: [
            for (int i = 0; i < children.length; i++) ...[
              children[i],
              if (i < children.length - 1) SizedBox(height: adaptiveSpacing),
            ],
          ],
        ),
      );
    }
  }
}

/// Widget qui adapte automatiquement les grilles pour mobile
class MobileAdaptiveGrid extends StatelessWidget {
  /// Liste des widgets enfants
  final List<Widget> children;
  
  /// Nombre de colonnes sur desktop
  final int desktopColumns;
  
  /// Nombre de colonnes sur mobile
  final int mobileColumns;
  
  /// Espacement entre les éléments
  final double spacing;
  
  /// Padding de la grille
  final EdgeInsets? padding;
  
  /// Ratio d'aspect des éléments
  final double childAspectRatio;

  const MobileAdaptiveGrid({
    super.key,
    required this.children,
    this.desktopColumns = 3,
    this.mobileColumns = 1,
    this.spacing = 16.0,
    this.padding,
    this.childAspectRatio = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    final isMobile = PlatformUtils.shouldUseMobileOptimizations(context);
    final columns = isMobile ? mobileColumns : desktopColumns;
    
    // Ajuster l'espacement pour mobile
    final adaptiveSpacing = isMobile ? spacing * 0.75 : spacing;
    
    // Ajuster le padding pour mobile
    final adaptivePadding = padding ?? 
        (isMobile 
            ? const EdgeInsets.all(16.0)
            : const EdgeInsets.all(24.0));

    return Padding(
      padding: adaptivePadding,
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: columns,
          crossAxisSpacing: adaptiveSpacing,
          mainAxisSpacing: adaptiveSpacing,
          childAspectRatio: childAspectRatio,
        ),
        itemCount: children.length,
        itemBuilder: (context, index) => children[index],
      ),
    );
  }
}

/// Widget qui adapte automatiquement les cartes pour mobile
class MobileAdaptiveCard extends StatelessWidget {
  /// Contenu de la carte
  final Widget child;
  
  /// Marge de la carte
  final EdgeInsets? margin;
  
  /// Padding de la carte
  final EdgeInsets? padding;
  
  /// Couleur de fond
  final Color? backgroundColor;
  
  /// Élévation de la carte
  final double? elevation;
  
  /// Callback pour les interactions
  final VoidCallback? onTap;

  const MobileAdaptiveCard({
    super.key,
    required this.child,
    this.margin,
    this.padding,
    this.backgroundColor,
    this.elevation,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isMobile = PlatformUtils.shouldUseMobileOptimizations(context);
    
    // Ajuster le padding pour mobile
    final adaptivePadding = padding ?? 
        (isMobile 
            ? const EdgeInsets.all(16.0)
            : const EdgeInsets.all(20.0));
    
    // Ajuster la marge pour mobile
    final adaptiveMargin = margin ?? 
        (isMobile 
            ? const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0)
            : const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0));

    final card = Card(
      margin: adaptiveMargin,
      elevation: elevation ?? (isMobile ? 2.0 : 4.0),
      color: backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: PlatformUtils.getBorderRadius(isLarge: !isMobile),
      ),
      child: Padding(
        padding: adaptivePadding,
        child: child,
      ),
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: PlatformUtils.getBorderRadius(isLarge: !isMobile),
        child: card,
      );
    }

    return card;
  }
}
