import 'package:flutter/material.dart';
import '../../utils/platform_utils.dart';
import '../../utils/responsive_utils.dart';

/// ListTile optimisé pour les interactions mobiles
class MobileListTile extends StatelessWidget {
  /// Widget principal (titre)
  final Widget? title;
  
  /// Widget secondaire (sous-titre)
  final Widget? subtitle;
  
  /// Widget à gauche
  final Widget? leading;
  
  /// Widget à droite
  final Widget? trailing;
  
  /// Callback pour les interactions tap
  final VoidCallback? onTap;
  
  /// Callback pour les interactions long press
  final VoidCallback? onLongPress;
  
  /// Si l'élément est sélectionné
  final bool selected;
  
  /// Couleur de fond
  final Color? backgroundColor;
  
  /// Couleur de fond quand sélectionné
  final Color? selectedColor;
  
  /// Padding personnalisé
  final EdgeInsets? contentPadding;
  
  /// Densité visuelle
  final VisualDensity? visualDensity;
  
  /// Si le feedback haptique est activé
  final bool enableHapticFeedback;
  
  /// Type de feedback haptique
  final HapticFeedbackType hapticFeedbackType;
  
  /// Si l'élément doit avoir un diviseur en bas
  final bool showDivider;
  
  /// Couleur du diviseur
  final Color? dividerColor;

  const MobileListTile({
    super.key,
    this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.onLongPress,
    this.selected = false,
    this.backgroundColor,
    this.selectedColor,
    this.contentPadding,
    this.visualDensity,
    this.enableHapticFeedback = true,
    this.hapticFeedbackType = HapticFeedbackType.light,
    this.showDivider = false,
    this.dividerColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isMobile = PlatformUtils.shouldUseMobileOptimizations(context);
    
    // Ajuster le padding pour mobile
    final adaptivePadding = contentPadding ?? 
        (isMobile 
            ? const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0)
            : const EdgeInsets.symmetric(horizontal: 20.0, vertical: 8.0));
    
    // Ajuster la densité visuelle pour mobile
    final adaptiveVisualDensity = visualDensity ?? 
        (isMobile ? VisualDensity.comfortable : VisualDensity.standard);

    final listTile = ListTile(
      title: title,
      subtitle: subtitle,
      leading: leading,
      trailing: trailing,
      selected: selected,
      tileColor: backgroundColor,
      selectedTileColor: selectedColor ?? 
          theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
      contentPadding: adaptivePadding,
      visualDensity: adaptiveVisualDensity,
      minVerticalPadding: isMobile ? 12.0 : 8.0,
      minLeadingWidth: isMobile ? 32.0 : 24.0,
      horizontalTitleGap: isMobile ? 16.0 : 12.0,
      shape: RoundedRectangleBorder(
        borderRadius: PlatformUtils.getBorderRadius(),
      ),
      onTap: onTap != null ? () {
        if (enableHapticFeedback && PlatformUtils.isMobile) {
          PlatformUtils.triggerHapticFeedback(hapticFeedbackType);
        }
        onTap!();
      } : null,
      onLongPress: onLongPress != null ? () {
        if (enableHapticFeedback && PlatformUtils.isMobile) {
          PlatformUtils.triggerHapticFeedback(HapticFeedbackType.medium);
        }
        onLongPress!();
      } : null,
    );

    if (showDivider) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          listTile,
          Divider(
            height: 1,
            thickness: 1,
            color: dividerColor ?? theme.dividerColor,
            indent: leading != null ? (isMobile ? 64.0 : 56.0) : 16.0,
            endIndent: 16.0,
          ),
        ],
      );
    }

    return listTile;
  }
}

/// ListTile avec avatar optimisé pour mobile
class MobileAvatarListTile extends StatelessWidget {
  /// URL ou chemin de l'image d'avatar
  final String? avatarUrl;
  
  /// Initiales à afficher si pas d'image
  final String? initials;
  
  /// Titre principal
  final String title;
  
  /// Sous-titre optionnel
  final String? subtitle;
  
  /// Widget à droite
  final Widget? trailing;
  
  /// Callback pour l'interaction
  final VoidCallback? onTap;
  
  /// Callback pour l'interaction longue
  final VoidCallback? onLongPress;
  
  /// Callback pour l'interaction avec l'avatar
  final VoidCallback? onAvatarTap;
  
  /// Taille de l'avatar
  final double? avatarSize;
  
  /// Couleur de fond de l'avatar
  final Color? avatarBackgroundColor;
  
  /// Si l'élément est sélectionné
  final bool selected;

  const MobileAvatarListTile({
    super.key,
    this.avatarUrl,
    this.initials,
    required this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.onLongPress,
    this.onAvatarTap,
    this.avatarSize,
    this.avatarBackgroundColor,
    this.selected = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isMobile = PlatformUtils.shouldUseMobileOptimizations(context);
    
    final adaptiveAvatarSize = avatarSize ?? (isMobile ? 48.0 : 40.0);

    Widget avatar;
    if (avatarUrl != null && avatarUrl!.isNotEmpty) {
      avatar = CircleAvatar(
        radius: adaptiveAvatarSize / 2,
        backgroundImage: NetworkImage(avatarUrl!),
        backgroundColor: avatarBackgroundColor ?? theme.colorScheme.primaryContainer,
        onBackgroundImageError: (_, __) {},
        child: avatarUrl!.isEmpty && initials != null
            ? Text(
                initials!,
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.w600,
                ),
              )
            : null,
      );
    } else {
      avatar = CircleAvatar(
        radius: adaptiveAvatarSize / 2,
        backgroundColor: avatarBackgroundColor ?? theme.colorScheme.primaryContainer,
        child: initials != null
            ? Text(
                initials!,
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.w600,
                ),
              )
            : Icon(
                Icons.person,
                size: adaptiveAvatarSize * 0.6,
                color: theme.colorScheme.onPrimaryContainer,
              ),
      );
    }

    if (onAvatarTap != null) {
      avatar = GestureDetector(
        onTap: () {
          if (PlatformUtils.isMobile) {
            PlatformUtils.triggerHapticFeedback(HapticFeedbackType.light);
          }
          onAvatarTap!();
        },
        child: avatar,
      );
    }

    return MobileListTile(
      leading: avatar,
      title: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            )
          : null,
      trailing: trailing,
      onTap: onTap,
      onLongPress: onLongPress,
      selected: selected,
    );
  }
}

/// ListTile avec icône optimisé pour mobile
class MobileIconListTile extends StatelessWidget {
  /// Icône à afficher
  final IconData icon;
  
  /// Titre principal
  final String title;
  
  /// Sous-titre optionnel
  final String? subtitle;
  
  /// Widget à droite
  final Widget? trailing;
  
  /// Callback pour l'interaction
  final VoidCallback? onTap;
  
  /// Couleur de l'icône
  final Color? iconColor;
  
  /// Couleur de fond de l'icône
  final Color? iconBackgroundColor;
  
  /// Taille de l'icône
  final double? iconSize;
  
  /// Si l'icône doit avoir un fond circulaire
  final bool circularIcon;
  
  /// Si l'élément est sélectionné
  final bool selected;

  const MobileIconListTile({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.iconColor,
    this.iconBackgroundColor,
    this.iconSize,
    this.circularIcon = true,
    this.selected = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isMobile = PlatformUtils.shouldUseMobileOptimizations(context);
    
    final adaptiveIconSize = iconSize ?? (isMobile ? 24.0 : 20.0);
    final containerSize = adaptiveIconSize + 16.0;

    Widget iconWidget = Icon(
      icon,
      size: adaptiveIconSize,
      color: iconColor ?? theme.colorScheme.primary,
    );

    if (circularIcon) {
      iconWidget = Container(
        width: containerSize,
        height: containerSize,
        decoration: BoxDecoration(
          color: iconBackgroundColor ?? theme.colorScheme.primaryContainer,
          shape: BoxShape.circle,
        ),
        child: Center(child: iconWidget),
      );
    }

    return MobileListTile(
      leading: iconWidget,
      title: Text(
        title,
        style: theme.textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.w600,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: subtitle != null
          ? Text(
              subtitle!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            )
          : null,
      trailing: trailing,
      onTap: onTap,
      selected: selected,
    );
  }
}
