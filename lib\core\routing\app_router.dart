import 'package:go_router/go_router.dart';
import 'package:seqqo/features/projets/screens/client_project_dashboard_screen.dart';
import 'package:seqqo/features/projets/screens/internal/project_detail_screen.dart'
    as internal; // Import pour ProjectDetailScreen interne
import 'package:seqqo/features/projets/widgets/project_edit_dialog.dart'; // Import pour ProjectEditDialog
import '../../features/company_directory/screens/company_detail_screen.dart';
import '../../features/company_directory/screens/company_form_screen.dart'; // Import CompanyFormScreen
import '../../features/company_directory/screens/company_directory_screen.dart'; // Import CompanyDirectoryScreen
import '../../features/settings/screens/settings_page.dart'; // Import SettingsPage
import 'package:flutter/material.dart'; // Pour MaterialPage et NoTransitionPage
import 'package:seqqo/features/navigation/screens/main_navigation_screen.dart'; // Import écran de navigation principale
import 'package:seqqo/features/site_report_v2/screens/site_report_list_screen.dart'; // Import pour SiteReportListScreen
import 'package:seqqo/features/site_report_v2/screens/site_report_form_screen.dart'; // Import pour SiteReportFormScreen
import 'package:seqqo/features/organizations/screens/organization_create_screen.dart';
import 'package:seqqo/features/organizations/screens/organization_settings_screen.dart';
import 'package:seqqo/features/organizations/screens/organization_members_screen.dart';
import 'package:seqqo/features/organizations/screens/organization_selector_screen.dart';
import 'package:seqqo/features/organizations/screens/organization_detail_screen.dart';
import 'package:seqqo/features/organizations/screens/firestore_rules_screen.dart';
import 'package:seqqo/features/project_companies/screens/project_companies_screen.dart'; // Import pour ProjectCompaniesScreen
import 'package:seqqo/features/projets/models/project_data.dart'; // Import pour ProjectData
import 'package:seqqo/features/projets/services/project_storage_service.dart'; // Import pour ProjectStorageService
import 'package:seqqo/features/acceptance_reports/screens/document_signature_screen.dart'; // Import pour DocumentSignatureScreen
import 'package:seqqo/features/auth/screens/login_screen.dart'; // Import pour LoginScreen
import 'package:seqqo/features/auth/screens/register_screen.dart'; // Import pour RegisterScreen
import 'package:seqqo/features/auth/screens/forgot_password_screen.dart'; // Import pour ForgotPasswordScreen
import 'package:firebase_auth/firebase_auth.dart';
import 'package:seqqo/features/organization/screens/organization_setup_screen.dart';
import 'package:seqqo/features/landing/screens/landing_screen.dart';
import 'package:seqqo/features/landing/screens/features_screen.dart';
import 'package:seqqo/features/landing/screens/pricing_screen.dart';
import 'package:seqqo/features/landing/screens/about_screen.dart';
import 'package:seqqo/features/landing/screens/blog_screen.dart';
import 'package:seqqo/features/landing/screens/contact_screen.dart';
import 'package:seqqo/features/landing/screens/privacy_screen.dart';
import 'package:seqqo/features/landing/screens/terms_screen.dart';
import 'package:seqqo/features/landing/screens/cookies_screen.dart';
import 'package:seqqo/features/clients/screens/client_list_screen.dart';
import 'package:seqqo/features/home/<USER>/dashboard_riverpod_screen.dart';
import 'package:seqqo/features/projets/screens/project_master_detail_screen.dart';
import 'package:seqqo/core/widgets/ui/theme_showcase.dart';
import 'auth_notifier.dart';

final authNotifier = AuthNotifier();

// Clé de navigateur globale pour le ShellRoute
final GlobalKey<NavigatorState> _shellNavigatorKey =
    GlobalKey<NavigatorState>();

// Instance de GoRouter utilisée pour la navigation - Optimisée Flutter 3.32
final GoRouter appRouter = GoRouter(
  refreshListenable: authNotifier,
  initialLocation: '/',
  debugLogDiagnostics: false, // Optimisation production

  // Gestion d'erreur 404 moderne
  errorBuilder: (context, state) => Scaffold(
    appBar: AppBar(title: const Text('Page non trouvée')),
    body: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          Text('Page non trouvée: ${state.matchedLocation}'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => context.go('/dashboard'),
            child: const Text('Retour au tableau de bord'),
          ),
        ],
      ),
    ),
  ),

  // Redirection optimisée avec cache
  redirect: (context, state) {
    final isAuthenticated = FirebaseAuth.instance.currentUser != null;
    final location = state.matchedLocation;

    // Routes publiques (optimisé avec Set pour performance)
    const publicRoutes = {
      '/',
      '/features',
      '/pricing',
      '/about',
      '/blog',
      '/contact',
      '/privacy',
      '/terms',
      '/cookies',
      '/login',
      '/register',
      '/forgot-password'
    };

    final isPublicRoute = publicRoutes.contains(location);

    // Redirection optimisée
    if (!isAuthenticated && !isPublicRoute) {
      return '/';
    }

    if (isAuthenticated &&
        (location == '/' || location == '/login' || location == '/register')) {
      return '/dashboard';
    }

    return null;
  },
  routes: [
    // Routes marketing avec transitions optimisées
    GoRoute(
      path: '/',
      pageBuilder: (context, state) => _buildFadePage(
        key: state.pageKey,
        child: const LandingScreen(),
      ),
    ),
    GoRoute(
      path: '/features',
      pageBuilder: (context, state) => _buildFadePage(
        key: state.pageKey,
        child: const FeaturesScreen(),
      ),
    ),
    GoRoute(
      path: '/pricing',
      pageBuilder: (context, state) => _buildFadePage(
        key: state.pageKey,
        child: const PricingScreen(),
      ),
    ),
    GoRoute(
      path: '/about',
      pageBuilder: (context, state) => _buildFadePage(
        key: state.pageKey,
        child: const AboutScreen(),
      ),
    ),
    GoRoute(
      path: '/blog',
      pageBuilder: (context, state) => _buildFadePage(
        key: state.pageKey,
        child: const BlogScreen(),
      ),
    ),
    GoRoute(
      path: '/contact',
      pageBuilder: (context, state) => _buildFadePage(
        key: state.pageKey,
        child: const ContactScreen(),
      ),
    ),
    GoRoute(
      path: '/privacy',
      pageBuilder: (context, state) => _buildFadePage(
        key: state.pageKey,
        child: const PrivacyScreen(),
      ),
    ),
    GoRoute(
      path: '/terms',
      pageBuilder: (context, state) => _buildFadePage(
        key: state.pageKey,
        child: const TermsScreen(),
      ),
    ),
    GoRoute(
      path: '/cookies',
      pageBuilder: (context, state) => _buildFadePage(
        key: state.pageKey,
        child: const CookiesScreen(),
      ),
    ),

    // Routes d'authentification avec transitions optimisées
    GoRoute(
      path: '/login',
      pageBuilder: (context, state) => _buildSlidePage(
        key: state.pageKey,
        child: const LoginScreen(),
        begin: const Offset(0.0, 1.0), // Glissement depuis le bas
      ),
    ),
    GoRoute(
      path: '/register',
      pageBuilder: (context, state) => _buildSlidePage(
        key: state.pageKey,
        child: const RegisterScreen(),
        begin: const Offset(1.0, 0.0), // Glissement depuis la droite
      ),
    ),
    GoRoute(
      path: '/forgot-password',
      pageBuilder: (context, state) => _buildFadePage(
        key: state.pageKey,
        child: const ForgotPasswordScreen(),
      ),
    ),

    // Routes de l'application avec une coque (Shell) - Optimisées Flutter 3.32
    ShellRoute(
      navigatorKey: _shellNavigatorKey,
      builder: (context, state, child) {
        return MainNavigationScreen(child: child);
      },
      routes: [
        GoRoute(
          path: '/dashboard',
          name: 'dashboard',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            child: const DashboardRiverpodScreen(),
          ),
        ),
        GoRoute(
          path: '/projects',
          name: 'projects',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            child: const ProjectMasterDetailScreen(),
          ),
        ),
        GoRoute(
          path: '/clients',
          name: 'clients',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            child: const ClientListScreen(),
          ),
        ),
        GoRoute(
          path: '/company-directory',
          name: 'company-directory',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            child: const CompanyDirectoryScreen(),
          ),
        ),
        GoRoute(
          path: '/settings',
          name: 'settings',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            child: const SettingsPage(),
          ),
        ),
        GoRoute(
          path: '/theme',
          name: 'theme',
          pageBuilder: (context, state) => NoTransitionPage(
            key: state.pageKey,
            child: const ThemeShowcase(),
          ),
        ),
      ],
    ),

    // Routes de l'application
    GoRoute(
      path: '/organization-setup',
      builder: (context, state) => const OrganizationSetupScreen(),
    ),
    GoRoute(
      path: '/suivi/:projectId',
      builder: (context, state) {
        final projectId = state.pathParameters['projectId']!;
        return ClientProjectDashboardScreen(projectId: projectId);
      },
    ),
    GoRoute(
      path: '/project/detail/:projectId',
      name: AppRoute.projectDetail.name,
      builder: (context, state) {
        final projectId = state.pathParameters['projectId']!;
        return internal.ProjectDetailScreen(
          projectId: projectId,
          onBack: null,
          initialProjectData: null,
        );
      },
    ),
    GoRoute(
      path: '/project/new',
      name: AppRoute.projectNew.name,
      builder: (context, state) {
        return Dialog(
          child: SizedBox(
            width: 800,
            child: const ProjectEditDialog(),
          ),
        );
      },
    ),
    GoRoute(
      path: '/company-directory/:companyId',
      name: AppRoute.companyDetail.name,
      pageBuilder: (context, state) {
        final id = state.pathParameters['companyId']!;
        return MaterialPage(
          key: state.pageKey,
          child: CompanyDetailScreen(companyId: id),
        );
      },
    ),
    GoRoute(
      path: '/company-directory/add',
      name: AppRoute.companyAdd.name,
      pageBuilder: (context, state) => MaterialPage(
        key: state.pageKey,
        child: const CompanyFormScreen(),
      ),
    ),

    // Route pour la gestion des entreprises d'un projet
    GoRoute(
      path: '/projects/:projectId/companies',
      name: AppRoute.projectCompanies.name,
      builder: (context, state) {
        final projectId = state.pathParameters['projectId']!;
        // Utiliser un FutureBuilder pour récupérer le nom du projet
        return FutureBuilder<ProjectData?>(
          future: ProjectStorageService().loadProject(projectId),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Scaffold(
                body: Center(child: CircularProgressIndicator()),
              );
            }

            final projectName = snapshot.data?.projectName ?? 'Projet';
            return ProjectCompaniesScreen(
              projectId: projectId,
              projectName: projectName,
            );
          },
        );
      },
    ),

    // Routes pour les rapports de chantier
    GoRoute(
      path: '/projects/:projectId/reports',
      name: AppRoute.siteReportList.name,
      builder: (context, state) {
        final projectId = state.pathParameters['projectId']!;
        return SiteReportListScreen(projectId: projectId);
      },
    ),
    GoRoute(
      path: '/projects/:projectId/reports/new',
      name: AppRoute.siteReportCreate.name,
      builder: (context, state) {
        final projectId = state.pathParameters['projectId']!;
        return SiteReportFormScreen(projectId: projectId);
      },
    ),
    GoRoute(
      path: '/projects/:projectId/reports/:reportId',
      name: AppRoute.siteReportEdit.name,
      builder: (context, state) {
        final projectId = state.pathParameters['projectId']!;
        final reportId = state.pathParameters['reportId']!;
        return SiteReportFormScreen(
          projectId: projectId,
          reportId: reportId,
        );
      },
    ),
    // Routes pour les organisations
    GoRoute(
      path: '/organizations/create',
      name: AppRoute.organizationCreate.name,
      builder: (context, state) => const OrganizationCreateScreen(),
    ),
    GoRoute(
      path: '/organizations/select',
      name: AppRoute.organizationSelect.name,
      builder: (context, state) => const OrganizationSelectorScreen(),
    ),
    GoRoute(
      path: '/organizations/settings',
      name: AppRoute.organizationSettings.name,
      builder: (context, state) => const OrganizationSettingsScreen(),
    ),
    GoRoute(
      path: '/organizations/:organizationId/settings',
      name: AppRoute.organizationSettingsWithId.name,
      builder: (context, state) {
        final organizationId = state.pathParameters['organizationId']!;
        return OrganizationSettingsScreen(organizationId: organizationId);
      },
    ),
    GoRoute(
      path: '/organizations/:organizationId/members',
      name: AppRoute.organizationMembers.name,
      builder: (context, state) {
        final organizationId = state.pathParameters['organizationId']!;
        return OrganizationMembersScreen(organizationId: organizationId);
      },
    ),
    GoRoute(
      path: '/organizations/:organizationId/detail',
      name: AppRoute.organizationDetail.name,
      builder: (context, state) {
        final organizationId = state.pathParameters['organizationId']!;
        return OrganizationDetailScreen(organizationId: organizationId);
      },
    ),
    // Route de migration supprimée
    GoRoute(
      path: '/organizations/rules',
      name: AppRoute.firestoreRules.name,
      builder: (context, state) => const FirestoreRulesScreen(),
    ),

    // Route pour la signature de document
    GoRoute(
      path: '/document/sign/:shareId',
      name: AppRoute.documentSign.name,
      builder: (context, state) {
        final shareId = state.pathParameters['shareId']!;
        final organizationId = state.uri.queryParameters['org'];
        return DocumentSignatureScreen(
          shareId: shareId,
          organizationId: organizationId,
        );
      },
    ),

    // Route pour le planning (redirige vers l'écran de détail du projet)
    GoRoute(
      path: '/planning/:planningId',
      name: AppRoute.planning.name,
      builder: (context, state) {
        // Récupérer le projectId depuis les paramètres de requête
        final projectId = state.uri.queryParameters['projectId'] ?? '';

        // Rediriger vers l'écran de détail du projet
        return internal.ProjectDetailScreen(
          projectId: projectId,
        );
      },
    ),
    // Routes de démonstration supprimées
  ],
);

/// Page personnalisée sans transition d'animation
class NoTransitionPage<T> extends Page<T> {
  final Widget child;

  const NoTransitionPage({
    required this.child,
    super.key,
    super.name,
    super.arguments,
    super.restorationId,
  });

  @override
  Route<T> createRoute(BuildContext context) {
    return PageRouteBuilder<T>(
      settings: this,
      pageBuilder: (context, animation, secondaryAnimation) => child,
      transitionDuration: Duration.zero,
      reverseTransitionDuration: Duration.zero,
    );
  }
}

// Fonctions d'aide pour les transitions optimisées Flutter 3.32

/// Page avec transition de fondu optimisée
Page<void> _buildFadePage({
  required LocalKey key,
  required Widget child,
  Duration duration = const Duration(milliseconds: 200),
}) {
  return CustomTransitionPage(
    key: key,
    child: child,
    transitionsBuilder: (context, animation, secondaryAnimation, child) {
      return FadeTransition(
        opacity: CurveTween(curve: Curves.easeInOut).animate(animation),
        child: child,
      );
    },
    transitionDuration: duration,
  );
}

/// Page avec transition de glissement optimisée
Page<void> _buildSlidePage({
  required LocalKey key,
  required Widget child,
  Offset begin = const Offset(1.0, 0.0),
  Duration duration = const Duration(milliseconds: 300),
}) {
  return CustomTransitionPage(
    key: key,
    child: child,
    transitionsBuilder: (context, animation, secondaryAnimation, child) {
      return SlideTransition(
        position: Tween<Offset>(
          begin: begin,
          end: Offset.zero,
        ).animate(CurveTween(curve: Curves.easeInOutCubic).animate(animation)),
        child: child,
      );
    },
    transitionDuration: duration,
  );
}

/// Page avec transition d'échelle optimisée
Page<void> _buildScalePage({
  required LocalKey key,
  required Widget child,
  Duration duration = const Duration(milliseconds: 250),
}) {
  return CustomTransitionPage(
    key: key,
    child: child,
    transitionsBuilder: (context, animation, secondaryAnimation, child) {
      return ScaleTransition(
        scale: CurveTween(curve: Curves.easeInOutBack).animate(animation),
        child: child,
      );
    },
    transitionDuration: duration,
  );
}

/// Page de transition personnalisée moderne
class CustomTransitionPage<T> extends Page<T> {
  final Widget child;
  final Widget Function(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) transitionsBuilder;
  final Duration transitionDuration;
  final Duration reverseTransitionDuration;

  const CustomTransitionPage({
    required this.child,
    required this.transitionsBuilder,
    this.transitionDuration = const Duration(milliseconds: 300),
    this.reverseTransitionDuration = const Duration(milliseconds: 300),
    super.key,
    super.name,
    super.arguments,
    super.restorationId,
  });

  @override
  Route<T> createRoute(BuildContext context) {
    return PageRouteBuilder<T>(
      settings: this,
      pageBuilder: (context, animation, secondaryAnimation) => child,
      transitionsBuilder: transitionsBuilder,
      transitionDuration: transitionDuration,
      reverseTransitionDuration: reverseTransitionDuration,
      maintainState: true, // Optimisation mémoire
    );
  }
}

enum AppRoute {
  splash, // Route pour l'écran de démarrage
  home,
  login, // Route pour l'écran de connexion
  register, // Route pour l'écran d'inscription
  forgotPassword, // Route pour l'écran de réinitialisation du mot de passe
  settings,
  companyDirectory,
  companyDetail,
  companyAdd, // Route for add company form
  projectDetail, // Route pour les détails du projet
  projectNew, // Route pour la création d'un projet
  projectCompanies, // Route pour la gestion des entreprises d'un projet
  siteReportList, // Route pour la liste des rapports de chantier
  siteReportCreate, // Route pour la création d'un rapport de chantier
  siteReportEdit, // Route pour l'édition d'un rapport de chantier
  organizationCreate, // Route pour la création d'une organisation
  organizationSelect, // Route pour la sélection d'une organisation
  organizationSettings, // Route pour les paramètres de l'organisation actuelle
  organizationSettingsWithId, // Route pour les paramètres d'une organisation spécifique
  organizationMembers, // Route pour la gestion des membres d'une organisation
  organizationDetail, // Route pour les détails d'une organisation
  firestoreRules, // Route pour les règles de sécurité Firestore
  documentSign, // Route pour la signature de document
  planning, // Route pour le planning
}
