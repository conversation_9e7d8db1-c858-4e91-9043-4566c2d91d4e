name: seqqo
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.5.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_colorpicker: ^1.1.0
  gantt_chart: ^0.3.0
  flutter_dropzone: ^4.2.1

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  path_provider: ^2.1.1
  json_annotation: ^4.9.0
  path: ^1.9.1
  intl: ^0.20.2
  uuid: ^4.5.1
  shared_preferences: ^2.2.2
  package_info_plus: ^8.3.0
  printing: ^5.14.2
  pdf: ^3.11.3

  logging: ^1.3.0
  image_picker: ^1.0.5
  file_picker: ^10.1.9
  url_launcher: ^6.2.2
  google_mlkit_text_recognition: ^0.13.1
  share_plus: ^11.0.0
  firebase_core: ^3.13.1
  cloud_firestore: ^5.6.8
  firebase_storage: ^12.4.6
  flutter_riverpod: ^2.6.1
  timeline_tile: ^2.0.0
  flutter_svg: ^2.0.9
  table_calendar: ^3.1.3
  firebase_auth: ^5.5.4
  google_sign_in: ^6.3.0
  go_router: ^15.1.3
  http: ^1.4.0
  flutter_dotenv: ^5.1.0
  percent_indicator: ^4.2.5
  fl_chart: ^1.0.0
  geolocator: ^14.0.1
  data_table_2: ^2.6.0
  flutter_localizations:
    sdk: flutter
  font_awesome_flutter: ^10.8.0
  google_fonts: ^6.2.1
  favicon: ^1.1.2
  flutter_local_notifications: ^19.2.1
  pdfx: ^2.9.1
  freezed_annotation: ^2.4.4
  connectivity_plus: ^6.1.4
  crypto: ^3.0.6
  cached_network_image: ^3.3.0
  flutter_image_compress: ^2.4.0
  flutter_cache_manager: ^3.4.1
  infinite_scroll_pagination: ^5.0.0
  photo_view: ^0.15.0
  syncfusion_flutter_pdfviewer: ^29.2.9
  flutter_staggered_grid_view: ^0.7.0
  flutter_hooks: ^0.21.2
  hooks_riverpod: ^2.4.9
  universal_html: ^2.2.4
  flutter_markdown: ^0.7.7+1
  form_validator: ^2.1.1
  flutter_bloc: ^9.1.1
  logger: ^2.5.0
  flutter_animate: ^4.5.0
  riverpod_annotation: ^2.6.1
  firebase_analytics: ^11.4.6
  firebase_messaging: ^15.2.6
  permission_handler: ^12.0.0+1
  google_maps_flutter: ^2.5.0
  timeago: ^3.6.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  build_runner: ^2.4.13
  flutter_launcher_icons: ^0.14.3
  json_serializable: ^6.8.0
  freezed: ^2.5.7
  riverpod_generator: ^2.6.4
  flutter_native_splash: ^2.3.8

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    # Configuration files
    - .env
    - assets/.env

    # Images
    - assets/images/
    - assets/images/google_logo.png

    # Icons
    - assets/icon/
    - assets/icon/app_icon.png
    - assets/icon/app_icon_foreground.png

    # Make sure these assets are also copied to web/assets/ for web deployment

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

  fonts:
    - family: RedditSans
      fonts:
        - asset: assets/fonts/RedditSans-Bold.ttf
          weight: 700
        - asset: assets/fonts/RedditSans-Regular.ttf
          weight: 400
    - family: Noto
      fonts:
        - asset: assets/fonts/NotoSans-Regular.ttf
        - asset: assets/fonts/NotoSans-Bold.ttf
          weight: 700

flutter_launcher_icons:
  # --- Configuration Générale ---
  # Chemin vers votre image source (carrée, haute résolution)
  image_path: "assets/icon/app_icon.png"

  # --- Android ---
  android: true
  # Pour les icônes adaptatives (Android 8+)
  adaptive_icon_background: "#25cc97"
  adaptive_icon_foreground: "assets/icon/app_icon_foreground.png"

  # --- iOS ---
  ios: true
  remove_alpha_ios: true

  # --- Web ---
  web:
    generate: true
    image_path: "assets/icon/app_icon.png"

  # --- Autres plateformes (Optionnel) ---
  # windows:
  #   generate: true
  #   image_path: "path/to/windows_icon.png"
  #   icon_size: 48
  # macos:
  #   generate: true
  #   image_path: "path/to/macos_icon.png"



flutter_native_splash:
  color: "#FFFFFF"
  image: assets/images/splash.png
  android_12:
    image: assets/images/splash.png
    icon_background_color: "#FFFFFF"
  web: false

# Configuration pour les icônes de l'application Seqqo
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/seqqo_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icons/seqqo_icon.png"
    background_color: "#2D2D2D"
    theme_color: "#2D2D2D"
  windows:
    generate: true
    image_path: "assets/icons/seqqo_icon.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/icons/seqqo_icon.png"
