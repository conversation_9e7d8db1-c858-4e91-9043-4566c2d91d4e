import 'package:flutter/material.dart';
import 'package:seqqo/features/clients/models/client_data.dart';
import 'package:seqqo/features/clients/widgets/client_edit_dialog.dart';
import 'package:seqqo/features/clients/widgets/client_list_widget.dart';
import 'package:seqqo/features/clients/services/client_storage_service.dart';
import 'package:seqqo/core/widgets/segmented_button.dart';
import 'package:seqqo/core/widgets/mobile_layout_wrapper.dart';
import 'package:seqqo/core/widgets/mobile_components/mobile_fab.dart';
import 'package:seqqo/core/widgets/mobile_components/mobile_card.dart';
import 'package:seqqo/core/utils/platform_utils.dart';
import 'package:seqqo/core/utils/responsive_utils.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';

// Extension pour ajouter les propriétés manquantes à ClientData
extension ClientDataExtensions on ClientData {
  String get updatedAt {
    try {
      final date = createdAt?.toDate() ?? DateTime.now();
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return 'Date inconnue';
    }
  }
}

class ClientListScreen extends StatefulWidget {
  const ClientListScreen({super.key});

  @override
  State<ClientListScreen> createState() => _ClientListScreenState();
}

class _ClientListScreenState extends State<ClientListScreen> {
  final Logger _logger = Logger('ClientListScreen');
  final List<ClientData> _clients = [];
  List<ClientData> _filteredClients = [];
  ClientData? _selectedClient;
  bool _isLoading = true;
  final ClientStorageService _clientService = ClientStorageService();

  // Nouveaux contrôles pour la recherche et le filtrage
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _filterStatus = 'Tous'; // Options: 'Tous', 'Actif', 'Inactif'
  String _sortBy = 'Date'; // Options: 'Date', 'Nom', 'Domaine'

  @override
  void initState() {
    super.initState();
    _loadClients();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterClients() {
    setState(() {
      _filteredClients = _clients.where((client) {
        // Filtre par recherche
        final matchesSearch = _searchQuery.isEmpty ||
            (client.name?.toLowerCase().contains(_searchQuery.toLowerCase()) ??
                false) ||
            (client.email?.toLowerCase().contains(_searchQuery.toLowerCase()) ??
                false) ||
            (client.enseigneType
                    ?.toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ??
                false) ||
            (client.mainContact?.interlocuteur
                    ?.toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ??
                false) ||
            (client.mainContact?.societe
                    ?.toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ??
                false);

        // Filtre par statut
        final matchesStatus = _filterStatus == 'Tous' ||
            (_filterStatus == 'Actif' && (client.statut == 'Actif')) ||
            (_filterStatus == 'Inactif' && (client.statut == 'Inactif'));

        return matchesSearch && matchesStatus;
      }).toList();

      // Tri
      switch (_sortBy) {
        case 'Date':
          _filteredClients.sort((a, b) {
            final dateA = a.createdAt?.toDate() ?? DateTime(0);
            final dateB = b.createdAt?.toDate() ?? DateTime(0);
            return dateB.compareTo(dateA);
          });
          break;
        case 'Nom':
          _filteredClients
              .sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
          break;
        case 'Type d\'enseigne':
          _filteredClients.sort(
              (a, b) => (a.enseigneType ?? '').compareTo(b.enseigneType ?? ''));
          break;
      }
    });
  }

  Future<void> _loadClients() async {
    setState(() => _isLoading = true);
    try {
      final clients = await _clientService.listClients();
      setState(() {
        _clients.clear();
        _clients.addAll(clients);
        _isLoading = false;
        _filteredClients = List.from(_clients);
        _filterClients();
      });
    } catch (e) {
      _logger.severe('Erreur lors du chargement des clients: $e');
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors du chargement des clients'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _confirmDeleteClient(ClientData client) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: Text(
            'Êtes-vous sûr de vouloir supprimer le client "${client.name}" ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _clientService.deleteClient(client.clientId);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Client supprimé avec succès')),
          );
        }
        _loadClients(); // Recharger la liste
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Erreur lors de la suppression: $e')),
          );
        }
      }
    }
  }

  Widget _buildClientListPane(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      color: theme.colorScheme.surface,
      child: Column(
        children: [
          // En-tête avec recherche et filtres
          _buildSearchHeader(),

          // Liste des clients
          Expanded(
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 16.0, right: 16.0, bottom: 16.0),
              child: ClientListWidget(
                clients: _filteredClients,
                selectedClient: _selectedClient,
                isLoading: _isLoading,
                onClientSelected: (client) {
                  setState(() {
                    _selectedClient = client;
                  });
                  // Ouvrir la boîte de dialogue d'édition lorsqu'on clique sur un client
                  showGeneralDialog(
                    context: context,
                    barrierDismissible: true,
                    barrierLabel: 'Modifier client',
                    barrierColor: Colors.black54,
                    transitionDuration: const Duration(milliseconds: 300),
                    pageBuilder: (context, animation, secondaryAnimation) =>
                        ClientEditDialog(
                      initialClient: client,
                      onClientSaved: () => _loadClients(),
                    ),
                    transitionBuilder:
                        (context, animation, secondaryAnimation, child) {
                      return FadeTransition(
                        opacity: animation,
                        child: ScaleTransition(
                          scale: CurvedAnimation(
                            parent: animation,
                            curve: Curves.easeOutCubic,
                          ),
                          child: child,
                        ),
                      );
                    },
                  );
                },
                onDeleteClient: (client) {
                  _confirmDeleteClient(client);
                },
                onOpenFullPage: (client) {
                  showGeneralDialog(
                    context: context,
                    barrierDismissible: true,
                    barrierLabel: 'Modifier client',
                    barrierColor: Colors.black54,
                    transitionDuration: const Duration(milliseconds: 300),
                    pageBuilder: (context, animation, secondaryAnimation) =>
                        ClientEditDialog(
                      initialClient: client,
                      onClientSaved: () => _loadClients(),
                    ),
                    transitionBuilder:
                        (context, animation, secondaryAnimation, child) {
                      return FadeTransition(
                        opacity: animation,
                        child: ScaleTransition(
                          scale: CurvedAnimation(
                            parent: animation,
                            curve: Curves.easeOutCubic,
                          ),
                          child: child,
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchHeader() {
    final theme = Theme.of(context);
    final isMobile = ResponsiveUtils.isMobile(context);

    // Status filter segments - adaptés pour mobile
    final statusSegments = [
      ButtonSegment<String>(
        value: 'Tous',
        label: Text('Tous', style: TextStyle(fontSize: isMobile ? 12 : 14)),
      ),
      ButtonSegment<String>(
        value: 'Actif',
        label: Text('Actif', style: TextStyle(fontSize: isMobile ? 12 : 14)),
        icon: Icon(Icons.check_circle_outline, size: isMobile ? 16 : 18),
      ),
      ButtonSegment<String>(
        value: 'Prospect',
        label: Text('Prospect', style: TextStyle(fontSize: isMobile ? 12 : 14)),
        icon: Icon(Icons.lightbulb_outline, size: isMobile ? 16 : 18),
      ),
      ButtonSegment<String>(
        value: 'Inactif',
        label: Text('Inactif', style: TextStyle(fontSize: isMobile ? 12 : 14)),
        icon: Icon(Icons.pause_circle_outline, size: isMobile ? 16 : 18),
      ),
    ];

    // Sort filter segments - adaptés pour mobile
    final sortSegments = [
      ButtonSegment<String>(
        value: 'Date',
        label: Text('Date', style: TextStyle(fontSize: isMobile ? 12 : 14)),
        icon: Icon(Icons.calendar_today_outlined, size: isMobile ? 16 : 18),
      ),
      ButtonSegment<String>(
        value: 'Nom',
        label: Text('Nom', style: TextStyle(fontSize: isMobile ? 12 : 14)),
        icon: Icon(Icons.sort_by_alpha, size: isMobile ? 16 : 18),
      ),
      ButtonSegment<String>(
        value: 'Client',
        label: Text('Client', style: TextStyle(fontSize: isMobile ? 12 : 14)),
        icon: Icon(Icons.business, size: isMobile ? 16 : 18),
      ),
    ];

    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(
        top: ResponsiveUtils.getVerticalPadding(context) * 0.5,
        left: ResponsiveUtils.getHorizontalPadding(context),
        right: ResponsiveUtils.getHorizontalPadding(context),
        bottom: ResponsiveUtils.getSpacing(context),
      ),
      padding: EdgeInsets.symmetric(
        vertical: ResponsiveUtils.getVerticalPadding(context),
        horizontal: ResponsiveUtils.getHorizontalPadding(context),
      ),
      decoration: BoxDecoration(
        border: Border(
            bottom: BorderSide(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
                width: 1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Première ligne: Recherche et boutons - adaptée pour mobile
          if (isMobile) ...[
            // Sur mobile: recherche en pleine largeur
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Rechercher par nom, client...',
                hintStyle: TextStyle(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                  fontSize: 14,
                ),
                prefixIcon: Icon(
                  Icons.search,
                  size: ResponsiveUtils.getIconSize(context),
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                ),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: Icon(
                          Icons.clear,
                          size: ResponsiveUtils.getIconSize(context,
                              isSmall: true),
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.5),
                        ),
                        onPressed: () {
                          setState(() {
                            _searchController.clear();
                            _searchQuery = '';
                            _filterClients();
                          });
                        },
                        tooltip: 'Effacer la recherche',
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: PlatformUtils.getBorderRadius(),
                  borderSide: BorderSide(
                      color: theme.colorScheme.outline.withValues(alpha: 0.5)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: PlatformUtils.getBorderRadius(),
                  borderSide: BorderSide(
                      color: theme.colorScheme.outline.withValues(alpha: 0.5)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: PlatformUtils.getBorderRadius(),
                  borderSide: BorderSide(
                      color: theme.colorScheme.primary.withValues(alpha: 0.7)),
                ),
                filled: true,
                fillColor: theme.colorScheme.surface,
                contentPadding: EdgeInsets.symmetric(
                  horizontal:
                      ResponsiveUtils.getHorizontalPadding(context) * 0.75,
                  vertical: ResponsiveUtils.getVerticalPadding(context) * 0.5,
                ),
                isDense: true,
              ),
              style: const TextStyle(fontSize: 14),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                  _filterClients();
                });
              },
            ),
            SizedBox(height: ResponsiveUtils.getSpacing(context)),
            // Boutons d'action sur mobile
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _loadClients,
                    icon: Icon(Icons.refresh,
                        size: ResponsiveUtils.getIconSize(context,
                            isSmall: true)),
                    label: const Text('Actualiser'),
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        vertical:
                            ResponsiveUtils.getVerticalPadding(context) * 0.5,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ] else ...[
            // Sur desktop: layout horizontal
            Row(
              children: [
                // Barre de recherche avec style Notion
                Expanded(
                  child: Tooltip(
                    message: 'Rechercher des clients par nom ou statut',
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Rechercher par nom, client...',
                        hintStyle: TextStyle(
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.5),
                          fontSize: 14,
                        ),
                        suffixIcon: _searchQuery.isNotEmpty
                            ? IconButton(
                                icon: Icon(
                                  Icons.clear,
                                  size: 18,
                                  color: theme.colorScheme.onSurface
                                      .withValues(alpha: 0.5),
                                ),
                                onPressed: () {
                                  setState(() {
                                    _searchController.clear();
                                    _searchQuery = '';
                                    _filterClients();
                                  });
                                },
                                tooltip: 'Effacer la recherche',
                              )
                            : null,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6.0),
                          borderSide: BorderSide(
                              color: theme.colorScheme.outline
                                  .withValues(alpha: 0.5)),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6.0),
                          borderSide: BorderSide(
                              color: theme.colorScheme.outline
                                  .withValues(alpha: 0.5)),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6.0),
                          borderSide: BorderSide(
                              color: theme.colorScheme.primary
                                  .withValues(alpha: 0.7)),
                        ),
                        filled: true,
                        fillColor: theme.colorScheme.surface,
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 10),
                        isDense: true,
                      ),
                      style: const TextStyle(fontSize: 14),
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value;
                          _filterClients();
                        });
                      },
                    ),
                  ),
                ),

                const SizedBox(width: 12),

                // Bouton de rafraîchissement
                Tooltip(
                  message: 'Rafraîchir la liste des clients',
                  child: Material(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(20),
                    child: InkWell(
                      onTap: _loadClients,
                      borderRadius: BorderRadius.circular(20),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Icon(
                          Icons.refresh,
                          size: 20,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Bouton d'ajout
                Tooltip(
                  message: 'Créer un nouveau client',
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.add, size: 18),
                    label:
                        const Text('Nouveau', style: TextStyle(fontSize: 14)),
                    onPressed: () => _showClientEditDialog(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],

          SizedBox(height: ResponsiveUtils.getSpacing(context)),

          const SizedBox(height: 16),

          // Filtres et tri - adaptés pour mobile
          if (isMobile) ...[
            // Sur mobile: filtres en colonne
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Statut',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                  ),
                ),
                SizedBox(
                    height:
                        ResponsiveUtils.getSpacing(context, isLarge: false)),
                AppSegmentedButton<String>(
                  segments: statusSegments,
                  selected: {_filterStatus},
                  isCompact: true,
                  onSelectionChanged: (Set<String> selection) {
                    if (ResponsiveUtils.isMobile(context)) {
                      PlatformUtils.triggerHapticFeedback(
                          HapticFeedbackType.light);
                    }
                    setState(() {
                      _filterStatus = selection.first;
                      _filterClients();
                    });
                  },
                ),
                SizedBox(height: ResponsiveUtils.getSpacing(context)),
                Text(
                  'Trier par',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                  ),
                ),
                SizedBox(
                    height:
                        ResponsiveUtils.getSpacing(context, isLarge: false)),
                AppSegmentedButton<String>(
                  segments: sortSegments,
                  selected: {_sortBy},
                  isCompact: true,
                  onSelectionChanged: (Set<String> selection) {
                    if (ResponsiveUtils.isMobile(context)) {
                      PlatformUtils.triggerHapticFeedback(
                          HapticFeedbackType.light);
                    }
                    setState(() {
                      _sortBy = selection.first;
                      _filterClients();
                    });
                  },
                ),
              ],
            ),
          ] else ...[
            // Sur desktop: filtres en ligne
            Row(
              children: [
                // Filtre par statut
                Expanded(
                  flex: 3,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Statut',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.8),
                        ),
                      ),
                      const SizedBox(height: 6),
                      Tooltip(
                        message: 'Filtrer les clients par statut',
                        child: AppSegmentedButton<String>(
                          segments: statusSegments,
                          selected: {_filterStatus},
                          isCompact: false,
                          onSelectionChanged: (Set<String> selection) {
                            setState(() {
                              _filterStatus = selection.first;
                              _filterClients();
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // Tri
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Trier par',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.8),
                        ),
                      ),
                      const SizedBox(height: 6),
                      Tooltip(
                        message: 'Trier les clients',
                        child: AppSegmentedButton<String>(
                          segments: sortSegments,
                          selected: {_sortBy},
                          isCompact: false,
                          onSelectionChanged: (Set<String> selection) {
                            setState(() {
                              _sortBy = selection.first;
                              _filterClients();
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],

          const SizedBox(height: 16),

          // Statistiques - adaptées pour mobile
          if (isMobile) ...[
            // Sur mobile: grille 2x2
            MobileAdaptiveGrid(
              mobileColumns: 2,
              desktopColumns: 4,
              spacing: ResponsiveUtils.getSpacing(context),
              childAspectRatio: 1.5,
              children: [
                _buildStatCard2(
                  context,
                  Icons.people,
                  _clients.length.toString(),
                  'Clients',
                  theme.colorScheme.primary,
                  'Nombre total de clients',
                ),
                _buildStatCard2(
                  context,
                  Icons.check_circle_outline,
                  _clients
                      .where((c) => (c.statut ?? '') == 'Actif')
                      .length
                      .toString(),
                  'Actifs',
                  Colors.green.shade700,
                  'Clients avec statut "Actif"',
                ),
                _buildStatCard2(
                  context,
                  Icons.lightbulb_outline,
                  _clients
                      .where((c) => (c.statut ?? '') == 'Prospect')
                      .length
                      .toString(),
                  'Prospects',
                  Colors.blue.shade700,
                  'Clients avec statut "Prospect"',
                ),
                _buildStatCard2(
                  context,
                  Icons.pause_circle_outline,
                  _clients
                      .where((c) => (c.statut ?? '') == 'Inactif')
                      .length
                      .toString(),
                  'Inactifs',
                  Colors.orange.shade800,
                  'Clients avec statut "Inactif"',
                ),
              ],
            ),
          ] else ...[
            // Sur desktop: ligne horizontale
            Row(
              children: [
                Expanded(
                  child: _buildStatCard2(
                    context,
                    Icons.people,
                    _clients.length.toString(),
                    'Clients',
                    theme.colorScheme.primary,
                    'Nombre total de clients',
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard2(
                    context,
                    Icons.check_circle_outline,
                    _clients
                        .where((c) => (c.statut ?? '') == 'Actif')
                        .length
                        .toString(),
                    'Actifs',
                    Colors.green.shade700,
                    'Clients avec statut "Actif"',
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard2(
                    context,
                    Icons.lightbulb_outline,
                    _clients
                        .where((c) => (c.statut ?? '') == 'Prospect')
                        .length
                        .toString(),
                    'Prospects',
                    Colors.blue.shade700,
                    'Clients avec statut "Prospect"',
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard2(
                    context,
                    Icons.pause_circle_outline,
                    _clients
                        .where((c) => (c.statut ?? '') == 'Inactif')
                        .length
                        .toString(),
                    'Inactifs',
                    Colors.orange.shade800,
                    'Clients avec statut "Inactif"',
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  // Widget pour afficher une statistique - optimisé pour mobile
  Widget _buildStatCard2(BuildContext context, IconData icon, String value,
      String label, Color color,
      [String? tooltip]) {
    final theme = Theme.of(context);
    final isMobile = ResponsiveUtils.isMobile(context);

    return Tooltip(
      message: tooltip ?? label,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: null, // Pour l'effet visuel uniquement
          hoverColor: color.withValues(alpha: 0.02),
          borderRadius: PlatformUtils.getBorderRadius(),
          child: Container(
            decoration: BoxDecoration(
              border:
                  Border.all(color: color.withValues(alpha: 0.15), width: 1),
              borderRadius: PlatformUtils.getBorderRadius(),
              color: color.withValues(alpha: 0.04),
            ),
            padding: EdgeInsets.symmetric(
              vertical: ResponsiveUtils.getVerticalPadding(context) * 0.5,
              horizontal: ResponsiveUtils.getHorizontalPadding(context) * 0.75,
            ),
            child: isMobile
                ? Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        icon,
                        color: color,
                        size: ResponsiveUtils.getIconSize(context),
                      ),
                      SizedBox(
                          height: ResponsiveUtils.getSpacing(context,
                              isLarge: false)),
                      Text(
                        value,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: isMobile ? 18 : 16,
                          color: color,
                        ),
                      ),
                      Text(
                        label,
                        style: TextStyle(
                          fontSize: isMobile ? 11 : 12,
                          fontWeight: FontWeight.w400,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Icon(
                        icon,
                        color: color,
                        size: ResponsiveUtils.getIconSize(context),
                      ),
                      SizedBox(width: ResponsiveUtils.getSpacing(context)),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            value,
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                              color: color,
                            ),
                          ),
                          Text(
                            label,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isMobile = ResponsiveUtils.isMobile(context);

    return Scaffold(
      // Suppression de l'AppBar pour un design plus moderne
      body: Stack(
        children: [
          _buildClientListPane(context),
          // Overlay de chargement
          if (_isLoading)
            Container(
              color: theme.colorScheme.surface.withValues(alpha: 0.7),
              child: Center(
                child: CircularProgressIndicator(
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
        ],
      ),
      // FAB optimisé pour mobile
      floatingActionButton: isMobile
          ? MobileExtendedFloatingActionButton(
              onPressed: () => _showClientEditDialog(),
              icon: const Icon(Icons.add),
              label: const Text('Nouveau client'),
              tooltip: 'Créer un nouveau client',
            )
          : null,
    );
  }

  void _showClientEditDialog([ClientData? client]) {
    // Ajouter feedback haptique sur mobile
    if (ResponsiveUtils.isMobile(context)) {
      PlatformUtils.triggerHapticFeedback(HapticFeedbackType.light);
    }

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: client == null ? 'Nouveau client' : 'Modifier client',
      barrierColor: Colors.black54,
      transitionDuration: PlatformUtils.getAnimationDuration(),
      pageBuilder: (context, animation, secondaryAnimation) => ClientEditDialog(
        initialClient: client,
        onClientSaved: () => _loadClients(),
      ),
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: CurvedAnimation(
              parent: animation,
              curve: PlatformUtils.getAnimationCurve(),
            ),
            child: child,
          ),
        );
      },
    );
  }
}
