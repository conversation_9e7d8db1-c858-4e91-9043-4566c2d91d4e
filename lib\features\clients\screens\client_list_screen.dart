import 'package:flutter/material.dart';
import 'package:seqqo/features/clients/models/client_data.dart';
import 'package:seqqo/features/clients/widgets/client_edit_dialog.dart';
import 'package:seqqo/features/clients/widgets/client_list_widget.dart';
import 'package:seqqo/features/clients/services/client_storage_service.dart';
import 'package:seqqo/core/widgets/segmented_button.dart';
import 'package:seqqo/core/widgets/mobile_components/mobile_toolbar.dart';
import 'package:seqqo/core/widgets/mobile_components/mobile_search_interface.dart';

import 'package:seqqo/core/widgets/mobile_components/skeleton_loader.dart';
import 'package:seqqo/core/widgets/mobile_components/modern_refresh_indicator.dart';
import 'package:seqqo/core/utils/platform_utils.dart';
import 'package:seqqo/core/utils/responsive_utils.dart';
import 'package:intl/intl.dart';
import 'package:logging/logging.dart';

// Extension pour ajouter les propriétés manquantes à ClientData
extension ClientDataExtensions on ClientData {
  String get updatedAt {
    try {
      final date = createdAt?.toDate() ?? DateTime.now();
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return 'Date inconnue';
    }
  }
}

class ClientListScreen extends StatefulWidget {
  const ClientListScreen({super.key});

  @override
  State<ClientListScreen> createState() => _ClientListScreenState();
}

class _ClientListScreenState extends State<ClientListScreen> {
  final Logger _logger = Logger('ClientListScreen');
  final List<ClientData> _clients = [];
  List<ClientData> _filteredClients = [];
  ClientData? _selectedClient;
  bool _isLoading = true;
  final ClientStorageService _clientService = ClientStorageService();

  // Nouveaux contrôles pour la recherche et le filtrage
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _filterStatus = 'Tous'; // Options: 'Tous', 'Actif', 'Inactif'
  String _sortBy = 'Date'; // Options: 'Date', 'Nom', 'Domaine'

  @override
  void initState() {
    super.initState();
    _loadClients();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterClients() {
    setState(() {
      _filteredClients = _clients.where((client) {
        // Filtre par recherche
        final matchesSearch = _searchQuery.isEmpty ||
            (client.name?.toLowerCase().contains(_searchQuery.toLowerCase()) ??
                false) ||
            (client.email?.toLowerCase().contains(_searchQuery.toLowerCase()) ??
                false) ||
            (client.enseigneType
                    ?.toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ??
                false) ||
            (client.mainContact?.interlocuteur
                    ?.toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ??
                false) ||
            (client.mainContact?.societe
                    ?.toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ??
                false);

        // Filtre par statut
        final matchesStatus = _filterStatus == 'Tous' ||
            (_filterStatus == 'Actif' && (client.statut == 'Actif')) ||
            (_filterStatus == 'Inactif' && (client.statut == 'Inactif'));

        return matchesSearch && matchesStatus;
      }).toList();

      // Tri
      switch (_sortBy) {
        case 'Date':
          _filteredClients.sort((a, b) {
            final dateA = a.createdAt?.toDate() ?? DateTime(0);
            final dateB = b.createdAt?.toDate() ?? DateTime(0);
            return dateB.compareTo(dateA);
          });
          break;
        case 'Nom':
          _filteredClients
              .sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
          break;
        case 'Type d\'enseigne':
          _filteredClients.sort(
              (a, b) => (a.enseigneType ?? '').compareTo(b.enseigneType ?? ''));
          break;
      }
    });
  }

  Future<void> _loadClients() async {
    setState(() => _isLoading = true);
    try {
      final clients = await _clientService.listClients();
      setState(() {
        _clients.clear();
        _clients.addAll(clients);
        _isLoading = false;
        _filteredClients = List.from(_clients);
        _filterClients();
      });
    } catch (e) {
      _logger.severe('Erreur lors du chargement des clients: $e');
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors du chargement des clients'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _confirmDeleteClient(ClientData client) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: Text(
            'Êtes-vous sûr de vouloir supprimer le client "${client.name}" ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _clientService.deleteClient(client.clientId);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Client supprimé avec succès')),
          );
        }
        _loadClients(); // Recharger la liste
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Erreur lors de la suppression: $e')),
          );
        }
      }
    }
  }

  Widget _buildClientListPane(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      color: theme.colorScheme.surface,
      child: Column(
        children: [
          // En-tête avec recherche et filtres
          _buildSearchHeader(),

          // Liste des clients
          Expanded(
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 16.0, right: 16.0, bottom: 16.0),
              child: ClientListWidget(
                clients: _filteredClients,
                selectedClient: _selectedClient,
                isLoading: _isLoading,
                onClientSelected: (client) {
                  setState(() {
                    _selectedClient = client;
                  });
                  // Ouvrir la boîte de dialogue d'édition lorsqu'on clique sur un client
                  showGeneralDialog(
                    context: context,
                    barrierDismissible: true,
                    barrierLabel: 'Modifier client',
                    barrierColor: Colors.black54,
                    transitionDuration: const Duration(milliseconds: 300),
                    pageBuilder: (context, animation, secondaryAnimation) =>
                        ClientEditDialog(
                      initialClient: client,
                      onClientSaved: () => _loadClients(),
                    ),
                    transitionBuilder:
                        (context, animation, secondaryAnimation, child) {
                      return FadeTransition(
                        opacity: animation,
                        child: ScaleTransition(
                          scale: CurvedAnimation(
                            parent: animation,
                            curve: Curves.easeOutCubic,
                          ),
                          child: child,
                        ),
                      );
                    },
                  );
                },
                onDeleteClient: (client) {
                  _confirmDeleteClient(client);
                },
                onOpenFullPage: (client) {
                  showGeneralDialog(
                    context: context,
                    barrierDismissible: true,
                    barrierLabel: 'Modifier client',
                    barrierColor: Colors.black54,
                    transitionDuration: const Duration(milliseconds: 300),
                    pageBuilder: (context, animation, secondaryAnimation) =>
                        ClientEditDialog(
                      initialClient: client,
                      onClientSaved: () => _loadClients(),
                    ),
                    transitionBuilder:
                        (context, animation, secondaryAnimation, child) {
                      return FadeTransition(
                        opacity: animation,
                        child: ScaleTransition(
                          scale: CurvedAnimation(
                            parent: animation,
                            curve: Curves.easeOutCubic,
                          ),
                          child: child,
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchHeader() {
    final theme = Theme.of(context);
    final isMobile = ResponsiveUtils.isMobile(context);

    // Status filter segments - adaptés pour mobile
    final statusSegments = [
      ButtonSegment<String>(
        value: 'Tous',
        label: Text('Tous',
            style: TextStyle(
                fontSize: isMobile ? 14 : 14, fontWeight: FontWeight.w500)),
      ),
      ButtonSegment<String>(
        value: 'Actif',
        label: Text('Actif',
            style: TextStyle(
                fontSize: isMobile ? 14 : 14, fontWeight: FontWeight.w500)),
        icon: Icon(Icons.check_circle_outline, size: isMobile ? 18 : 18),
      ),
      ButtonSegment<String>(
        value: 'Prospect',
        label: Text('Prospect',
            style: TextStyle(
                fontSize: isMobile ? 14 : 14, fontWeight: FontWeight.w500)),
        icon: Icon(Icons.lightbulb_outline, size: isMobile ? 18 : 18),
      ),
      ButtonSegment<String>(
        value: 'Inactif',
        label: Text('Inactif',
            style: TextStyle(
                fontSize: isMobile ? 14 : 14, fontWeight: FontWeight.w500)),
        icon: Icon(Icons.pause_circle_outline, size: isMobile ? 18 : 18),
      ),
    ];

    // Sort filter segments - adaptés pour mobile
    final sortSegments = [
      ButtonSegment<String>(
        value: 'Date',
        label: Text('Date',
            style: TextStyle(
                fontSize: isMobile ? 14 : 14, fontWeight: FontWeight.w500)),
        icon: Icon(Icons.calendar_today_outlined, size: isMobile ? 18 : 18),
      ),
      ButtonSegment<String>(
        value: 'Nom',
        label: Text('Nom',
            style: TextStyle(
                fontSize: isMobile ? 14 : 14, fontWeight: FontWeight.w500)),
        icon: Icon(Icons.sort_by_alpha, size: isMobile ? 18 : 18),
      ),
      ButtonSegment<String>(
        value: 'Client',
        label: Text('Client',
            style: TextStyle(
                fontSize: isMobile ? 14 : 14, fontWeight: FontWeight.w500)),
        icon: Icon(Icons.business, size: isMobile ? 18 : 18),
      ),
    ];

    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(
        top: ResponsiveUtils.getVerticalPadding(context) * 0.5,
        left: ResponsiveUtils.getHorizontalPadding(context),
        right: ResponsiveUtils.getHorizontalPadding(context),
        bottom: ResponsiveUtils.getSpacing(context),
      ),
      padding: EdgeInsets.symmetric(
        vertical: ResponsiveUtils.getVerticalPadding(context),
        horizontal: ResponsiveUtils.getHorizontalPadding(context),
      ),
      decoration: BoxDecoration(
        border: Border(
            bottom: BorderSide(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
                width: 1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Toolbar mobile ou recherche desktop
          if (isMobile) ...[
            // Sur mobile: toolbar moderne avec statistiques intégrées (style Projets)
            Column(
              children: [
                MobileToolbar(
                  title: 'Clients',
                  onSearchTap: _openMobileSearch,
                  onRefresh: _loadClients,
                  isLoading: _isLoading,
                  showFilterBadge: true,
                  activeFilterCount: _getActiveFilterCount(),
                  filterWidgets: [
                    // Filtre de statut
                    MobileFilterChip(
                      label: _filterStatus,
                      icon: _getStatusIcon(_filterStatus),
                      isSelected: _filterStatus != 'Tous',
                      selectedColor: _getStatusColor(_filterStatus),
                      onTap: () => _showStatusFilterDialog(),
                    ),
                    // Filtre de tri
                    MobileFilterChip(
                      label: _sortBy,
                      icon: _getSortIcon(_sortBy),
                      isSelected: _sortBy != 'Date',
                      onTap: () => _showSortFilterDialog(),
                    ),
                  ],
                ),

                // Statistiques compactes pour mobile (style Projets)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        _buildStatChip(
                          context,
                          Icons.people,
                          _clients.length,
                          'Clients',
                          theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        _buildStatChip(
                          context,
                          Icons.check_circle,
                          _clients
                              .where((c) => (c.statut ?? '') == 'Actif')
                              .length,
                          'Actifs',
                          Colors.green.shade600,
                        ),
                        const SizedBox(width: 8),
                        _buildStatChip(
                          context,
                          Icons.lightbulb,
                          _clients
                              .where((c) => (c.statut ?? '') == 'Prospect')
                              .length,
                          'Prospects',
                          Colors.blue.shade600,
                        ),
                        const SizedBox(width: 8),
                        _buildStatChip(
                          context,
                          Icons.pause_circle,
                          _clients
                              .where((c) => (c.statut ?? '') == 'Inactif')
                              .length,
                          'Inactifs',
                          Colors.orange.shade600,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ] else ...[
            // Sur desktop: layout horizontal
            Row(
              children: [
                // Barre de recherche avec style Notion
                Expanded(
                  child: Tooltip(
                    message: 'Rechercher des clients par nom ou statut',
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'Rechercher par nom, client...',
                        hintStyle: TextStyle(
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.5),
                          fontSize: 14,
                        ),
                        suffixIcon: _searchQuery.isNotEmpty
                            ? IconButton(
                                icon: Icon(
                                  Icons.clear,
                                  size: 18,
                                  color: theme.colorScheme.onSurface
                                      .withValues(alpha: 0.5),
                                ),
                                onPressed: () {
                                  setState(() {
                                    _searchController.clear();
                                    _searchQuery = '';
                                    _filterClients();
                                  });
                                },
                                tooltip: 'Effacer la recherche',
                              )
                            : null,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6.0),
                          borderSide: BorderSide(
                              color: theme.colorScheme.outline
                                  .withValues(alpha: 0.5)),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6.0),
                          borderSide: BorderSide(
                              color: theme.colorScheme.outline
                                  .withValues(alpha: 0.5)),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6.0),
                          borderSide: BorderSide(
                              color: theme.colorScheme.primary
                                  .withValues(alpha: 0.7)),
                        ),
                        filled: true,
                        fillColor: theme.colorScheme.surface,
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 10),
                        isDense: true,
                      ),
                      style: const TextStyle(fontSize: 14),
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value;
                          _filterClients();
                        });
                      },
                    ),
                  ),
                ),

                const SizedBox(width: 12),

                // Bouton de rafraîchissement
                Tooltip(
                  message: 'Rafraîchir la liste des clients',
                  child: Material(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(20),
                    child: InkWell(
                      onTap: _loadClients,
                      borderRadius: BorderRadius.circular(20),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Icon(
                          Icons.refresh,
                          size: 20,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Bouton d'ajout
                Tooltip(
                  message: 'Créer un nouveau client',
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.add, size: 18),
                    label:
                        const Text('Nouveau', style: TextStyle(fontSize: 14)),
                    onPressed: () => _showClientEditDialog(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],

          SizedBox(height: ResponsiveUtils.getSpacing(context)),

          // Filtres desktop uniquement (mobile utilise la toolbar)
          if (!isMobile) ...[
            // Sur desktop: filtres en ligne
            Row(
              children: [
                // Filtre par statut
                Expanded(
                  flex: 3,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Statut',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.8),
                        ),
                      ),
                      const SizedBox(height: 6),
                      Tooltip(
                        message: 'Filtrer les clients par statut',
                        child: AppSegmentedButton<String>(
                          segments: statusSegments,
                          selected: {_filterStatus},
                          isCompact: false,
                          onSelectionChanged: (Set<String> selection) {
                            setState(() {
                              _filterStatus = selection.first;
                              _filterClients();
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // Tri
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Trier par',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: theme.colorScheme.onSurface
                              .withValues(alpha: 0.8),
                        ),
                      ),
                      const SizedBox(height: 6),
                      Tooltip(
                        message: 'Trier les clients',
                        child: AppSegmentedButton<String>(
                          segments: sortSegments,
                          selected: {_sortBy},
                          isCompact: false,
                          onSelectionChanged: (Set<String> selection) {
                            setState(() {
                              _sortBy = selection.first;
                              _filterClients();
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],

          const SizedBox(height: 16),

          // Statistiques desktop uniquement (mobile intégré dans toolbar)
          if (!isMobile) ...[
            // Sur desktop: ligne horizontale
            Row(
              children: [
                Expanded(
                  child: _buildStatCard2(
                    context,
                    Icons.people,
                    _clients.length.toString(),
                    'Clients',
                    theme.colorScheme.primary,
                    'Nombre total de clients',
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard2(
                    context,
                    Icons.check_circle_outline,
                    _clients
                        .where((c) => (c.statut ?? '') == 'Actif')
                        .length
                        .toString(),
                    'Actifs',
                    Colors.green.shade700,
                    'Clients avec statut "Actif"',
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard2(
                    context,
                    Icons.lightbulb_outline,
                    _clients
                        .where((c) => (c.statut ?? '') == 'Prospect')
                        .length
                        .toString(),
                    'Prospects',
                    Colors.blue.shade700,
                    'Clients avec statut "Prospect"',
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard2(
                    context,
                    Icons.pause_circle_outline,
                    _clients
                        .where((c) => (c.statut ?? '') == 'Inactif')
                        .length
                        .toString(),
                    'Inactifs',
                    Colors.orange.shade800,
                    'Clients avec statut "Inactif"',
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  // Widget pour afficher un chip de statistique compact (mobile)
  Widget _buildStatChip(BuildContext context, IconData icon, int count,
      String label, Color color) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            '$count',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
          const SizedBox(width: 2),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  // Widget pour afficher une statistique - optimisé pour mobile
  Widget _buildStatCard2(BuildContext context, IconData icon, String value,
      String label, Color color,
      [String? tooltip]) {
    final theme = Theme.of(context);
    final isMobile = ResponsiveUtils.isMobile(context);

    return Tooltip(
      message: tooltip ?? label,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: null, // Pour l'effet visuel uniquement
          hoverColor: color.withValues(alpha: 0.02),
          borderRadius: PlatformUtils.getBorderRadius(),
          child: Container(
            decoration: BoxDecoration(
              border:
                  Border.all(color: color.withValues(alpha: 0.15), width: 1),
              borderRadius: PlatformUtils.getBorderRadius(),
              color: color.withValues(alpha: 0.04),
            ),
            padding: EdgeInsets.symmetric(
              vertical: ResponsiveUtils.getVerticalPadding(context) * 0.5,
              horizontal: ResponsiveUtils.getHorizontalPadding(context) * 0.75,
            ),
            child: isMobile
                ? Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        icon,
                        color: color,
                        size: ResponsiveUtils.getIconSize(context),
                      ),
                      SizedBox(
                          height: ResponsiveUtils.getSpacing(context,
                              isLarge: false)),
                      Text(
                        value,
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: isMobile ? 18 : 16,
                          color: color,
                        ),
                      ),
                      Text(
                        label,
                        style: TextStyle(
                          fontSize: isMobile ? 11 : 12,
                          fontWeight: FontWeight.w400,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Icon(
                        icon,
                        color: color,
                        size: ResponsiveUtils.getIconSize(context),
                      ),
                      SizedBox(width: ResponsiveUtils.getSpacing(context)),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            value,
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                              color: color,
                            ),
                          ),
                          Text(
                            label,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w400,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isMobile = ResponsiveUtils.isMobile(context);

    return Scaffold(
      // Suppression de l'AppBar pour un design plus moderne
      body: SafeArea(
        child: Stack(
          children: [
            _buildClientListPane(context),
            // Overlay de chargement
            if (_isLoading)
              Container(
                color: theme.colorScheme.surface.withValues(alpha: 0.7),
                child: Center(
                  child: CircularProgressIndicator(
                    color: theme.colorScheme.primary,
                  ),
                ),
              ),
          ],
        ),
      ),
      // FAB simple pour mobile
      floatingActionButton: isMobile
          ? FloatingActionButton.extended(
              onPressed: () => _showClientEditDialog(),
              icon: const Icon(Icons.add),
              label: const Text('Nouveau client'),
              tooltip: 'Créer un nouveau client',
            )
          : FloatingActionButton(
              onPressed: () => _showClientEditDialog(),
              tooltip: 'Ajouter un client',
              child: const Icon(Icons.add),
            ),
    );
  }

  void _showClientEditDialog([ClientData? client]) {
    // Ajouter feedback haptique sur mobile
    if (ResponsiveUtils.isMobile(context)) {
      PlatformUtils.triggerHapticFeedback(HapticFeedbackType.light);
    }

    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: client == null ? 'Nouveau client' : 'Modifier client',
      barrierColor: Colors.black54,
      transitionDuration: PlatformUtils.getAnimationDuration(),
      pageBuilder: (context, animation, secondaryAnimation) => ClientEditDialog(
        initialClient: client,
        onClientSaved: () => _loadClients(),
      ),
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: CurvedAnimation(
              parent: animation,
              curve: PlatformUtils.getAnimationCurve(),
            ),
            child: child,
          ),
        );
      },
    );
  }

  void _openMobileSearch() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => MobileSearchInterface(
          hintText: 'Rechercher des clients...',
          initialQuery: _searchQuery,
          onQueryChanged: (query) {
            setState(() {
              _searchQuery = query;
              _filterClients();
            });
          },
          onClear: () {
            setState(() {
              _searchQuery = '';
              _filterClients();
            });
          },
          // Suggestions basées sur les noms de clients
          suggestions: _searchQuery.isNotEmpty
              ? _clients
                  .where((client) =>
                      client.name
                          ?.toLowerCase()
                          .contains(_searchQuery.toLowerCase()) ??
                      false)
                  .take(5)
                  .map((client) => client.name ?? '')
                  .toList()
              : [],
        ),
      ),
    );
  }

  // Méthodes helper pour la toolbar moderne
  int _getActiveFilterCount() {
    int count = 0;
    if (_filterStatus != 'Tous') count++;
    if (_sortBy != 'Date') count++;
    return count;
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'Actif':
        return Icons.check_circle;
      case 'Prospect':
        return Icons.lightbulb;
      case 'Inactif':
        return Icons.pause_circle;
      default:
        return Icons.people;
    }
  }

  Color _getStatusColor(String status) {
    final theme = Theme.of(context);
    switch (status) {
      case 'Actif':
        return Colors.green.shade600;
      case 'Prospect':
        return Colors.blue.shade600;
      case 'Inactif':
        return Colors.orange.shade600;
      default:
        return theme.colorScheme.primary;
    }
  }

  IconData _getSortIcon(String sortBy) {
    switch (sortBy) {
      case 'Nom':
        return Icons.sort_by_alpha;
      case 'Client':
        return Icons.business;
      default:
        return Icons.calendar_today;
    }
  }

  void _showStatusFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _StatusFilterBottomSheet(
        currentStatus: _filterStatus,
        onStatusChanged: (status) {
          setState(() {
            _filterStatus = status;
            _filterClients();
          });
          Navigator.of(context).pop();
        },
      ),
    );
  }

  void _showSortFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _SortFilterBottomSheet(
        currentSort: _sortBy,
        onSortChanged: (sort) {
          setState(() {
            _sortBy = sort;
            _filterClients();
          });
          Navigator.of(context).pop();
        },
      ),
    );
  }
}

/// Bottom sheet pour le filtre de statut
class _StatusFilterBottomSheet extends StatelessWidget {
  final String currentStatus;
  final ValueChanged<String> onStatusChanged;

  const _StatusFilterBottomSheet({
    required this.currentStatus,
    required this.onStatusChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            offset: const Offset(0, -2),
            blurRadius: 8,
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: theme.colorScheme.outline.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Icon(
                    Icons.people,
                    color: theme.colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Filtrer par statut',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),

            // Options
            ...['Tous', 'Actif', 'Prospect', 'Inactif'].map((status) {
              final isSelected = status == currentStatus;
              return ListTile(
                leading: Icon(
                  _getStatusIconForBottomSheet(status),
                  color: isSelected
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                title: Text(
                  status,
                  style: TextStyle(
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    color: isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface,
                  ),
                ),
                trailing: isSelected
                    ? Icon(
                        Icons.check,
                        color: theme.colorScheme.primary,
                      )
                    : null,
                onTap: () => onStatusChanged(status),
              );
            }),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  IconData _getStatusIconForBottomSheet(String status) {
    switch (status) {
      case 'Actif':
        return Icons.check_circle;
      case 'Prospect':
        return Icons.lightbulb;
      case 'Inactif':
        return Icons.pause_circle;
      default:
        return Icons.people;
    }
  }
}

/// Bottom sheet pour le tri
class _SortFilterBottomSheet extends StatelessWidget {
  final String currentSort;
  final ValueChanged<String> onSortChanged;

  const _SortFilterBottomSheet({
    required this.currentSort,
    required this.onSortChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            offset: const Offset(0, -2),
            blurRadius: 8,
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: theme.colorScheme.outline.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Icon(
                    Icons.sort,
                    color: theme.colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Trier par',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),

            // Options
            ...['Date', 'Nom', 'Client'].map((sort) {
              final isSelected = sort == currentSort;
              return ListTile(
                leading: Icon(
                  _getSortIconForBottomSheet(sort),
                  color: isSelected
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
                title: Text(
                  sort,
                  style: TextStyle(
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    color: isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface,
                  ),
                ),
                trailing: isSelected
                    ? Icon(
                        Icons.check,
                        color: theme.colorScheme.primary,
                      )
                    : null,
                onTap: () => onSortChanged(sort),
              );
            }),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  IconData _getSortIconForBottomSheet(String sort) {
    switch (sort) {
      case 'Nom':
        return Icons.sort_by_alpha;
      case 'Client':
        return Icons.business;
      default:
        return Icons.calendar_today;
    }
  }
}
