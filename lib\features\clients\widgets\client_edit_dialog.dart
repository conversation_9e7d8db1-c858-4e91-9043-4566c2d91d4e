import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'dart:async';
import 'package:seqqo/features/clients/models/client_data.dart';
import 'package:seqqo/features/audit/models/contact_details.dart';
import 'package:seqqo/features/clients/services/client_storage_service.dart';
import 'package:seqqo/core/utils/feedback_utils.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:seqqo/core/widgets/modern_modal.dart';
import 'package:seqqo/core/widgets/app_button.dart';
import 'package:seqqo/core/utils/responsive_utils.dart';

class ClientEditDialog extends StatefulWidget {
  final ClientData? initialClient; // Null pour un nouveau client
  final Function?
      onClientSaved; // Callback appelé quand un client est sauvegardé

  const ClientEditDialog({
    super.key,
    this.initialClient,
    this.onClientSaved,
  });

  @override
  State<ClientEditDialog> createState() => _ClientEditDialogState();
}

class _ClientEditDialogState extends State<ClientEditDialog> {
  final _formKey = GlobalKey<FormState>();
  late ClientData _currentClient;
  late bool _isNewClient;
  bool _isSaving = false;
  Timer? _autoSaveTimer;
  bool _hasUnsavedChanges = false;
  String? _selectedEnseigneType; // Pour gérer la valeur du Dropdown
  String? _selectedStatut;

  // Contrôleurs pour les champs
  final _nameController = TextEditingController();
  // final _enseigneTypeController = TextEditingController(); // Remplacé par _selectedEnseigneType
  final _notesController = TextEditingController();
  final _contactSocieteController = TextEditingController();
  final _contactInterlocuteurController = TextEditingController();
  final _contactCoordonneesController = TextEditingController();
  // Contrôleurs facturation / juridique
  final _raisonSocialeController = TextEditingController();
  final _siretController = TextEditingController();
  final _adresseFacturationController = TextEditingController();
  final _codePostalFacturationController = TextEditingController();
  final _villeFacturationController = TextEditingController();
  final _paysFacturationController = TextEditingController();
  final _tvaIntracomController = TextEditingController();
  final _referenceJuridiqueController = TextEditingController();
  final _contactFacturationNomController = TextEditingController();
  final _contactFacturationEmailController = TextEditingController();
  final _contactFacturationTelController = TextEditingController();

  final ClientStorageService _clientService = ClientStorageService();
  final Uuid _uuid = const Uuid();

  final List<String> _statutOptions = [
    'Actif',
    'Prospect',
    'Inactif',
    'Archivé',
    'Blacklisté',
  ];

  @override
  void initState() {
    super.initState();
    _isNewClient = widget.initialClient == null;

    if (_isNewClient) {
      // Crée un nouveau client avec un nouvel ID et la date de création
      _currentClient = ClientData(
        clientId: _uuid.v4(),
        createdAt: Timestamp.now(),
      );
    } else {
      // Utilise le client existant passé en argument
      _currentClient = widget.initialClient!;
    }

    // Initialise les contrôleurs et la valeur du dropdown avec les données actuelles
    _nameController.text = _currentClient.name ?? '';
    _selectedEnseigneType =
        _currentClient.enseigneType; // Initialise la valeur du dropdown
    _selectedStatut = _currentClient.statut ?? 'Actif';
    _notesController.text = _currentClient.notes ?? '';
    _contactSocieteController.text = _currentClient.mainContact?.societe ?? '';
    _contactInterlocuteurController.text =
        _currentClient.mainContact?.interlocuteur ?? '';
    _contactCoordonneesController.text =
        _currentClient.mainContact?.coordonnees ?? '';
    // Init facturation / juridique
    _raisonSocialeController.text = _currentClient.raisonSociale ?? '';
    _siretController.text = _currentClient.siret ?? '';
    _adresseFacturationController.text =
        _currentClient.adresseFacturation ?? '';
    _codePostalFacturationController.text =
        _currentClient.codePostalFacturation ?? '';
    _villeFacturationController.text = _currentClient.villeFacturation ?? '';
    _paysFacturationController.text = _currentClient.paysFacturation ?? '';
    _tvaIntracomController.text = _currentClient.tvaIntracom ?? '';
    _referenceJuridiqueController.text =
        _currentClient.referenceJuridique ?? '';
    _contactFacturationNomController.text =
        _currentClient.contactFacturationNom ?? '';

    // Ajoute les listeners pour détecter les changements (sauf pour le dropdown géré par onChanged)
    _nameController.addListener(_onFieldChanged);
    // _enseigneTypeController.addListener(_onFieldChanged); // Remplacé par onChanged du Dropdown
    _notesController.addListener(_onFieldChanged);
    _contactSocieteController.addListener(_onFieldChanged);
    _contactInterlocuteurController.addListener(_onFieldChanged);
    _contactCoordonneesController.addListener(_onFieldChanged);
  }

  void _onFieldChanged() {
    _hasUnsavedChanges = true;
    _autoSaveTimer?.cancel();
    _autoSaveTimer = Timer(const Duration(seconds: 2), () {
      if (_hasUnsavedChanges && mounted) {
        _saveClient(showFeedback: false);
      }
    });
  }

  @override
  void dispose() {
    // Nettoie les contrôleurs
    _autoSaveTimer?.cancel();
    _nameController.dispose();
    // _enseigneTypeController.dispose(); // Plus de contrôleur pour enseigneType
    _notesController.dispose();
    _contactSocieteController.dispose();
    _contactInterlocuteurController.dispose();
    _contactCoordonneesController.dispose();
    // Dispose facturation / juridique
    _raisonSocialeController.dispose();
    _siretController.dispose();
    _adresseFacturationController.dispose();
    _codePostalFacturationController.dispose();
    _villeFacturationController.dispose();
    _paysFacturationController.dispose();
    _tvaIntracomController.dispose();
    _referenceJuridiqueController.dispose();
    _contactFacturationNomController.dispose();
    _contactFacturationEmailController.dispose();
    _contactFacturationTelController.dispose();
    super.dispose();
  }

  // Fonction de sauvegarde
  Future<void> _saveClient({bool showFeedback = true}) async {
    // Valide le formulaire
    if (!(_formKey.currentState?.validate() ?? false)) {
      if (showFeedback) {
        showFeedbackSnackBar(
          context,
          message: 'Veuillez corriger les erreurs.',
          isError: true,
        );
      }
      return;
    }
    if (_isSaving) return;

    setState(() {
      _isSaving = true;
    });

    // Met à jour l'objet _currentClient avec les valeurs des contrôleurs et du dropdown
    _currentClient.name = _nameController.text.trim();
    _currentClient.enseigneType =
        _selectedEnseigneType; // Utilise la valeur sélectionnée
    _currentClient.statut = _selectedStatut;
    _currentClient.notes = _notesController.text.trim();
    // Facturation / juridique
    _currentClient.raisonSociale = _raisonSocialeController.text.trim();
    _currentClient.siret = _siretController.text.trim();
    _currentClient.adresseFacturation =
        _adresseFacturationController.text.trim();
    _currentClient.codePostalFacturation =
        _codePostalFacturationController.text.trim();
    _currentClient.villeFacturation = _villeFacturationController.text.trim();
    _currentClient.paysFacturation = _paysFacturationController.text.trim();
    _currentClient.tvaIntracom = _tvaIntracomController.text.trim();
    _currentClient.referenceJuridique =
        _referenceJuridiqueController.text.trim();
    _currentClient.contactFacturationNom =
        _contactFacturationNomController.text.trim();

    // Gère l'objet contact imbriqué
    final contactSociete = _contactSocieteController.text.trim();
    final contactInterlocuteur = _contactInterlocuteurController.text.trim();
    final contactCoordonnees = _contactCoordonneesController.text.trim();
    final contactEmail = _contactFacturationEmailController.text.trim();
    final contactTelephone = _contactFacturationTelController.text.trim();

    if (contactSociete.isNotEmpty ||
        contactInterlocuteur.isNotEmpty ||
        contactCoordonnees.isNotEmpty ||
        contactEmail.isNotEmpty ||
        contactTelephone.isNotEmpty) {
      _currentClient.mainContact =
          (_currentClient.mainContact ?? ContactDetails()).copyWith(
        societe: contactSociete.isEmpty ? null : contactSociete,
        interlocuteur:
            contactInterlocuteur.isEmpty ? null : contactInterlocuteur,
        coordonnees: contactCoordonnees.isEmpty ? null : contactCoordonnees,
        email: contactEmail.isEmpty ? null : contactEmail,
        telephone: contactTelephone.isEmpty ? null : contactTelephone,
      );
    } else {
      _currentClient.mainContact =
          null; // Met à null si tous les champs contact sont vides
    }

    // Appelle le service pour sauvegarder
    bool success = false;
    try {
      success = await _clientService.saveClient(_currentClient);
      _hasUnsavedChanges = !success;
    } catch (e) {
      print("Erreur saveClient: $e");
      success = false;
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }

    // Feedback et retour
    if (mounted && showFeedback) {
      showFeedbackSnackBar(
        context,
        message: success ? 'Client sauvegardé !' : 'Erreur sauvegarde client.',
        isError: !success,
      );
      if (success) {
        if (widget.onClientSaved != null) {
          widget.onClientSaved!();
        }
        Navigator.pop(context); // Ferme le dialogue
      }
    }
  }

  // --- Fonctions utilitaires pour le Dropdown Enseigne Type ---

  final List<String> _enseigneTypes = [
    'Boutique',
    'Restaurant',
    'Bureau',
    'Hôtel',
    'Entrepôt',
    'Opticien',
    'Pharmacie',
    'Autre'
  ];

  IconData _getIconForType(String? type) {
    switch (type?.toLowerCase()) {
      case 'boutique':
        return Icons.storefront;
      case 'restaurant':
        return Icons.restaurant;
      case 'bureau':
        return Icons.business;
      case 'hôtel':
      case 'hotel':
        return Icons.hotel;
      case 'entrepôt':
      case 'entrepot':
        return Icons.warehouse;
      case 'opticien':
        return Icons.visibility;
      case 'pharmacie':
        return Icons.local_pharmacy;
      default:
        return Icons.category; // Icône par défaut
    }
  }

  Color _getColorForType(String? type) {
    switch (type?.toLowerCase()) {
      case 'boutique':
        return Colors.blue;
      case 'restaurant':
        return Colors.orange;
      case 'bureau':
        return Colors.indigo;
      case 'hôtel':
      case 'hotel':
        return Colors.purple;
      case 'entrepôt':
      case 'entrepot':
        return Colors.brown;
      case 'opticien':
        return Colors.lightBlue;
      case 'pharmacie':
        return Colors.green;
      default:
        return Colors.teal; // Couleur par défaut
    }
  }

  Widget _buildEnseigneTypeDropdown(BuildContext context) {
    final theme = Theme.of(context);
    // Assure que la valeur sélectionnée est valide ou null
    String? validSelectedType = _selectedEnseigneType != null &&
            _enseigneTypes.contains(_selectedEnseigneType)
        ? _selectedEnseigneType
        : null;

    return DropdownButtonFormField<String>(
      value: validSelectedType,
      hint: const Text('Sélectionner un type'),
      isExpanded: true,
      decoration: InputDecoration(
        labelText: 'Type d\'enseigne',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        filled: true,
        fillColor: theme.colorScheme.surfaceContainerLowest,
        prefixIcon: validSelectedType == null
            ? const Icon(Icons
                .category_outlined) // Icône générique quand rien n'est sélectionné
            : null, // Pas d'icône préfixe ici, gérée dans les items/selectedItemBuilder
      ),
      items: _enseigneTypes.map((String type) {
        return DropdownMenuItem<String>(
          value: type,
          child: Row(
            children: [
              Icon(
                _getIconForType(type),
                size: 20,
                color: _getColorForType(type),
              ),
              const SizedBox(width: 10),
              Text(type),
            ],
          ),
        );
      }).toList(),
      onChanged: (String? newValue) {
        setState(() {
          _selectedEnseigneType = newValue;
        });
        _onFieldChanged(); // Déclenche l'auto-save
      },
      // Optionnel: Personnaliser l'affichage de l'élément sélectionné dans le bouton
      selectedItemBuilder: (BuildContext context) {
        return _enseigneTypes.map<Widget>((String item) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _getIconForType(item),
                size: 16,
                color: _getColorForType(item),
              ),
              const SizedBox(width: 8),
              Flexible(
                child: Text(
                  item,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          );
        }).toList();
      },
    );
  }

  // --- Fin Fonctions utilitaires ---

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isMobile = ResponsiveUtils.isMobile(context);

    return ModernModal(
      title: _isNewClient ? 'Nouveau Client' : 'Modifier Client',
      maxHeight: isMobile ? null : MediaQuery.of(context).size.height * 0.85,
      actions: [
        AppButton.ghost(
          onPressed: () => Navigator.of(context).pop(),
          text: 'Annuler',
        ),
        AppButton.primary(
          onPressed: _isSaving ? null : () => _saveClient(),
          text: _isNewClient ? 'Créer' : 'Enregistrer',
          isLoading: _isSaving,
        ),
      ],
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section Informations générales
            _buildSectionHeader(
                theme, Icons.business, 'Informations générales'),
            const SizedBox(height: 16),

            ModernFormField(
              label: 'Nom du client *',
              hint: 'Nom de l\'entreprise ou du client',
              controller: _nameController,
              prefixIcon: const Icon(Icons.apartment_outlined),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Le nom est requis';
                }
                return null;
              },
              onChanged: (_) => _onFieldChanged(),
            ),
            const SizedBox(height: 16),

            _buildModernStatusDropdown(theme),
            const SizedBox(height: 16),

            _buildModernEnseigneTypeDropdown(theme),
            const SizedBox(height: 24),
            // Section Contact principal
            _buildSectionHeader(
                theme, Icons.person_outline, 'Contact principal'),
            const SizedBox(height: 16),

            ModernFormField(
              label: 'Société',
              hint: 'Nom de la société du contact',
              controller: _contactSocieteController,
              prefixIcon: const Icon(Icons.business_outlined),
              onChanged: (_) => _onFieldChanged(),
            ),
            const SizedBox(height: 16),

            ModernFormField(
              label: 'Interlocuteur',
              hint: 'Nom de la personne de contact',
              controller: _contactInterlocuteurController,
              prefixIcon: const Icon(Icons.person),
              onChanged: (_) => _onFieldChanged(),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: ModernFormField(
                    label: 'Email',
                    hint: '<EMAIL>',
                    controller: _contactFacturationEmailController,
                    prefixIcon: const Icon(Icons.email_outlined),
                    keyboardType: TextInputType.emailAddress,
                    onChanged: (_) => _onFieldChanged(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ModernFormField(
                    label: 'Téléphone',
                    hint: '01 23 45 67 89',
                    controller: _contactFacturationTelController,
                    prefixIcon: const Icon(Icons.phone_outlined),
                    keyboardType: TextInputType.phone,
                    onChanged: (_) => _onFieldChanged(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            ModernFormField(
              label: 'Coordonnées complémentaires',
              hint: 'Adresse, informations supplémentaires...',
              controller: _contactCoordonneesController,
              prefixIcon: const Icon(Icons.location_on_outlined),
              onChanged: (_) => _onFieldChanged(),
            ),
            const SizedBox(height: 24),
            // Section Notes
            _buildSectionHeader(theme, Icons.sticky_note_2_outlined, 'Notes'),
            const SizedBox(height: 16),

            ModernFormField(
              label: 'Notes',
              hint: 'Informations complémentaires sur le client',
              controller: _notesController,
              prefixIcon: const Icon(Icons.edit_note_outlined),
              maxLines: 3,
              onChanged: (_) => _onFieldChanged(),
            ),
            const SizedBox(height: 24),
            // Section Facturation & Juridique
            _buildSectionHeader(
                theme, Icons.receipt_long_outlined, 'Facturation & Juridique'),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: ModernFormField(
                    label: 'Raison sociale',
                    hint: 'Nom officiel de l\'entreprise',
                    controller: _raisonSocialeController,
                    prefixIcon: const Icon(Icons.business_center_outlined),
                    onChanged: (_) => _onFieldChanged(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ModernFormField(
                    label: 'SIRET',
                    hint: '12345678901234',
                    controller: _siretController,
                    prefixIcon: const Icon(Icons.confirmation_number_outlined),
                    onChanged: (_) => _onFieldChanged(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            ModernFormField(
              label: 'Adresse de facturation',
              hint: 'Adresse complète pour la facturation',
              controller: _adresseFacturationController,
              prefixIcon: const Icon(Icons.location_city_outlined),
              onChanged: (_) => _onFieldChanged(),
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  flex: 1,
                  child: ModernFormField(
                    label: 'Code postal',
                    hint: '75000',
                    controller: _codePostalFacturationController,
                    prefixIcon: const Icon(Icons.local_post_office_outlined),
                    keyboardType: TextInputType.number,
                    onChanged: (_) => _onFieldChanged(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 2,
                  child: ModernFormField(
                    label: 'Ville',
                    hint: 'Paris',
                    controller: _villeFacturationController,
                    prefixIcon: const Icon(Icons.location_city),
                    onChanged: (_) => _onFieldChanged(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 1,
                  child: ModernFormField(
                    label: 'Pays',
                    hint: 'France',
                    controller: _paysFacturationController,
                    prefixIcon: const Icon(Icons.public_outlined),
                    onChanged: (_) => _onFieldChanged(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: ModernFormField(
                    label: 'TVA intracommunautaire',
                    hint: 'FR12345678901',
                    controller: _tvaIntracomController,
                    prefixIcon: const Icon(Icons.numbers_outlined),
                    onChanged: (_) => _onFieldChanged(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ModernFormField(
                    label: 'Référence juridique',
                    hint: 'Référence interne',
                    controller: _referenceJuridiqueController,
                    prefixIcon: const Icon(Icons.gavel_outlined),
                    onChanged: (_) => _onFieldChanged(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            ModernFormField(
              label: 'Contact facturation',
              hint: 'Nom du contact pour la facturation',
              controller: _contactFacturationNomController,
              prefixIcon: const Icon(Icons.person_outline),
              onChanged: (_) => _onFieldChanged(),
            ),
          ],
        ),
      ),
    );
  }

  // Méthodes helper pour l'interface moderne
  Widget _buildSectionHeader(ThemeData theme, IconData icon, String title) {
    return Row(
      children: [
        Icon(icon, color: theme.colorScheme.primary, size: 20),
        const SizedBox(width: 8),
        Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.primary,
            letterSpacing: -0.2,
          ),
        ),
      ],
    );
  }

  Widget _buildModernStatusDropdown(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Statut du client',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface.withAlpha(180),
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedStatut,
          decoration: InputDecoration(
            prefixIcon: const Icon(Icons.flag_outlined),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: theme.colorScheme.outline.withAlpha(60),
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: theme.colorScheme.outline.withAlpha(40),
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: theme.colorScheme.primary,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: theme.colorScheme.surface,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
          items: _statutOptions
              .map((statut) => DropdownMenuItem<String>(
                    value: statut,
                    child: Text(statut),
                  ))
              .toList(),
          onChanged: (value) {
            setState(() {
              _selectedStatut = value;
            });
            _onFieldChanged();
          },
        ),
      ],
    );
  }

  Widget _buildModernEnseigneTypeDropdown(ThemeData theme) {
    String? validSelectedType = _selectedEnseigneType != null &&
            _enseigneTypes.contains(_selectedEnseigneType)
        ? _selectedEnseigneType
        : null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Type d\'enseigne',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface.withAlpha(180),
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: validSelectedType,
          hint: const Text('Sélectionner un type'),
          isExpanded: true,
          decoration: InputDecoration(
            prefixIcon: validSelectedType == null
                ? const Icon(Icons.category_outlined)
                : Icon(
                    _getIconForType(validSelectedType),
                    color: _getColorForType(validSelectedType),
                  ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: theme.colorScheme.outline.withAlpha(60),
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: theme.colorScheme.outline.withAlpha(40),
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: theme.colorScheme.primary,
                width: 2,
              ),
            ),
            filled: true,
            fillColor: theme.colorScheme.surface,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
          items: _enseigneTypes.map((String type) {
            return DropdownMenuItem<String>(
              value: type,
              child: Row(
                children: [
                  Icon(
                    _getIconForType(type),
                    size: 20,
                    color: _getColorForType(type),
                  ),
                  const SizedBox(width: 8),
                  Text(type),
                ],
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            setState(() {
              _selectedEnseigneType = newValue;
            });
            _onFieldChanged();
          },
        ),
      ],
    );
  }
}
