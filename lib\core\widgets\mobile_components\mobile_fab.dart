import 'package:flutter/material.dart';
import '../../utils/platform_utils.dart';
import '../../utils/responsive_utils.dart';

/// FloatingActionButton optimisé pour mobile
class MobileFloatingActionButton extends StatelessWidget {
  /// Callback pour l'action
  final VoidCallback? onPressed;
  
  /// Icône du bouton
  final Widget? child;
  
  /// Tooltip du bouton
  final String? tooltip;
  
  /// Couleur de fond
  final Color? backgroundColor;
  
  /// Couleur de premier plan
  final Color? foregroundColor;
  
  /// Élévation du bouton
  final double? elevation;
  
  /// Élévation quand pressé
  final double? highlightElevation;
  
  /// Forme du bouton
  final ShapeBorder? shape;
  
  /// Si le feedback haptique est activé
  final bool enableHapticFeedback;
  
  /// Type de feedback haptique
  final HapticFeedbackType hapticFeedbackType;
  
  /// Taille du bouton
  final MobileFabSize size;
  
  /// Tag héroïque pour les animations
  final Object? heroTag;

  const MobileFloatingActionButton({
    super.key,
    required this.onPressed,
    this.child,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.highlightElevation,
    this.shape,
    this.enableHapticFeedback = true,
    this.hapticFeedbackType = HapticFeedbackType.light,
    this.size = MobileFabSize.regular,
    this.heroTag,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isMobile = PlatformUtils.shouldUseMobileOptimizations(context);
    
    // Ajuster les propriétés pour mobile
    final adaptiveElevation = elevation ?? (isMobile ? 6.0 : 4.0);
    final adaptiveHighlightElevation = highlightElevation ?? (isMobile ? 12.0 : 8.0);
    
    // Déterminer la taille du bouton
    Widget fabWidget;
    switch (size) {
      case MobileFabSize.small:
        fabWidget = FloatingActionButton.small(
          onPressed: onPressed != null ? () {
            if (enableHapticFeedback && PlatformUtils.isMobile) {
              PlatformUtils.triggerHapticFeedback(hapticFeedbackType);
            }
            onPressed!();
          } : null,
          tooltip: tooltip,
          backgroundColor: backgroundColor,
          foregroundColor: foregroundColor,
          elevation: adaptiveElevation,
          highlightElevation: adaptiveHighlightElevation,
          shape: shape ?? (isMobile 
              ? RoundedRectangleBorder(borderRadius: BorderRadius.circular(16))
              : null),
          heroTag: heroTag,
          child: child,
        );
        break;
      case MobileFabSize.large:
        fabWidget = FloatingActionButton.large(
          onPressed: onPressed != null ? () {
            if (enableHapticFeedback && PlatformUtils.isMobile) {
              PlatformUtils.triggerHapticFeedback(hapticFeedbackType);
            }
            onPressed!();
          } : null,
          tooltip: tooltip,
          backgroundColor: backgroundColor,
          foregroundColor: foregroundColor,
          elevation: adaptiveElevation,
          highlightElevation: adaptiveHighlightElevation,
          shape: shape ?? (isMobile 
              ? RoundedRectangleBorder(borderRadius: BorderRadius.circular(28))
              : null),
          heroTag: heroTag,
          child: child,
        );
        break;
      case MobileFabSize.regular:
      default:
        fabWidget = FloatingActionButton(
          onPressed: onPressed != null ? () {
            if (enableHapticFeedback && PlatformUtils.isMobile) {
              PlatformUtils.triggerHapticFeedback(hapticFeedbackType);
            }
            onPressed!();
          } : null,
          tooltip: tooltip,
          backgroundColor: backgroundColor,
          foregroundColor: foregroundColor,
          elevation: adaptiveElevation,
          highlightElevation: adaptiveHighlightElevation,
          shape: shape ?? (isMobile 
              ? RoundedRectangleBorder(borderRadius: BorderRadius.circular(20))
              : null),
          heroTag: heroTag,
          child: child,
        );
        break;
    }

    return fabWidget;
  }
}

/// FloatingActionButton étendu optimisé pour mobile
class MobileExtendedFloatingActionButton extends StatelessWidget {
  /// Callback pour l'action
  final VoidCallback? onPressed;
  
  /// Icône du bouton
  final Widget? icon;
  
  /// Label du bouton
  final Widget label;
  
  /// Tooltip du bouton
  final String? tooltip;
  
  /// Couleur de fond
  final Color? backgroundColor;
  
  /// Couleur de premier plan
  final Color? foregroundColor;
  
  /// Élévation du bouton
  final double? elevation;
  
  /// Élévation quand pressé
  final double? highlightElevation;
  
  /// Forme du bouton
  final ShapeBorder? shape;
  
  /// Si le feedback haptique est activé
  final bool enableHapticFeedback;
  
  /// Type de feedback haptique
  final HapticFeedbackType hapticFeedbackType;
  
  /// Si le bouton est étendu
  final bool isExtended;
  
  /// Tag héroïque pour les animations
  final Object? heroTag;

  const MobileExtendedFloatingActionButton({
    super.key,
    required this.onPressed,
    required this.label,
    this.icon,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.highlightElevation,
    this.shape,
    this.enableHapticFeedback = true,
    this.hapticFeedbackType = HapticFeedbackType.light,
    this.isExtended = true,
    this.heroTag,
  });

  @override
  Widget build(BuildContext context) {
    final isMobile = PlatformUtils.shouldUseMobileOptimizations(context);
    
    // Ajuster les propriétés pour mobile
    final adaptiveElevation = elevation ?? (isMobile ? 6.0 : 4.0);
    final adaptiveHighlightElevation = highlightElevation ?? (isMobile ? 12.0 : 8.0);

    return FloatingActionButton.extended(
      onPressed: onPressed != null ? () {
        if (enableHapticFeedback && PlatformUtils.isMobile) {
          PlatformUtils.triggerHapticFeedback(hapticFeedbackType);
        }
        onPressed!();
      } : null,
      icon: icon,
      label: label,
      tooltip: tooltip,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      elevation: adaptiveElevation,
      highlightElevation: adaptiveHighlightElevation,
      shape: shape ?? (isMobile 
          ? RoundedRectangleBorder(borderRadius: BorderRadius.circular(20))
          : null),
      isExtended: isExtended,
      heroTag: heroTag,
    );
  }
}

/// Groupe de FloatingActionButtons pour mobile
class MobileFabGroup extends StatefulWidget {
  /// Bouton principal
  final MobileFloatingActionButton mainFab;
  
  /// Liste des boutons secondaires
  final List<MobileFabGroupItem> children;
  
  /// Si le groupe est ouvert par défaut
  final bool initiallyOpen;
  
  /// Direction d'ouverture
  final MobileFabGroupDirection direction;
  
  /// Espacement entre les boutons
  final double spacing;
  
  /// Couleur de l'overlay
  final Color? overlayColor;
  
  /// Si l'overlay doit être affiché
  final bool showOverlay;

  const MobileFabGroup({
    super.key,
    required this.mainFab,
    required this.children,
    this.initiallyOpen = false,
    this.direction = MobileFabGroupDirection.up,
    this.spacing = 16.0,
    this.overlayColor,
    this.showOverlay = true,
  });

  @override
  State<MobileFabGroup> createState() => _MobileFabGroupState();
}

class _MobileFabGroupState extends State<MobileFabGroup>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _expandAnimation;
  bool _isOpen = false;

  @override
  void initState() {
    super.initState();
    _isOpen = widget.initiallyOpen;
    _controller = AnimationController(
      duration: PlatformUtils.getAnimationDuration(),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _controller,
      curve: PlatformUtils.getAnimationCurve(),
    );
    
    if (_isOpen) {
      _controller.value = 1.0;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggle() {
    setState(() {
      _isOpen = !_isOpen;
      if (_isOpen) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
    
    if (PlatformUtils.isMobile) {
      PlatformUtils.triggerHapticFeedback(HapticFeedbackType.light);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        // Overlay
        if (widget.showOverlay)
          AnimatedBuilder(
            animation: _expandAnimation,
            builder: (context, child) {
              return _expandAnimation.value > 0
                  ? GestureDetector(
                      onTap: _toggle,
                      child: Container(
                        width: double.infinity,
                        height: double.infinity,
                        color: (widget.overlayColor ?? Colors.black)
                            .withValues(alpha: 0.3 * _expandAnimation.value),
                      ),
                    )
                  : const SizedBox.shrink();
            },
          ),
        
        // Boutons secondaires
        ...widget.children.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final offset = (index + 1) * (56.0 + widget.spacing);
          
          return AnimatedBuilder(
            animation: _expandAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: _getOffset(offset * _expandAnimation.value),
                child: Transform.scale(
                  scale: _expandAnimation.value,
                  child: Opacity(
                    opacity: _expandAnimation.value,
                    child: MobileFloatingActionButton(
                      onPressed: () {
                        item.onPressed();
                        _toggle();
                      },
                      tooltip: item.tooltip,
                      backgroundColor: item.backgroundColor,
                      foregroundColor: item.foregroundColor,
                      size: MobileFabSize.small,
                      heroTag: "fab_${index}_${item.tooltip}",
                      child: Icon(item.icon),
                    ),
                  ),
                ),
              );
            },
          );
        }),
        
        // Bouton principal
        MobileFloatingActionButton(
          onPressed: _toggle,
          tooltip: widget.mainFab.tooltip,
          backgroundColor: widget.mainFab.backgroundColor,
          foregroundColor: widget.mainFab.foregroundColor,
          size: widget.mainFab.size,
          heroTag: widget.mainFab.heroTag,
          child: AnimatedRotation(
            turns: _isOpen ? 0.125 : 0.0,
            duration: PlatformUtils.getAnimationDuration(),
            child: widget.mainFab.child,
          ),
        ),
      ],
    );
  }

  Offset _getOffset(double distance) {
    switch (widget.direction) {
      case MobileFabGroupDirection.up:
        return Offset(0, -distance);
      case MobileFabGroupDirection.down:
        return Offset(0, distance);
      case MobileFabGroupDirection.left:
        return Offset(-distance, 0);
      case MobileFabGroupDirection.right:
        return Offset(distance, 0);
    }
  }
}

/// Élément d'un groupe de FAB
class MobileFabGroupItem {
  final IconData icon;
  final String? tooltip;
  final VoidCallback onPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const MobileFabGroupItem({
    required this.icon,
    required this.onPressed,
    this.tooltip,
    this.backgroundColor,
    this.foregroundColor,
  });
}

/// Tailles disponibles pour les FAB mobiles
enum MobileFabSize {
  small,
  regular,
  large,
}

/// Directions d'ouverture pour les groupes de FAB
enum MobileFabGroupDirection {
  up,
  down,
  left,
  right,
}
