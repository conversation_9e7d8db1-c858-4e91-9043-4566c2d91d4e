import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../core/widgets/app_button.dart';
import '../../../core/widgets/app_card.dart';
import '../../../core/widgets/app_icon_button.dart';
import '../../../core/widgets/app_badge.dart';
import '../../../core/widgets/mobile_layout_wrapper.dart';
import '../../../core/widgets/mobile_components/mobile_card.dart';
import '../../../core/utils/platform_utils.dart';
import '../../../core/utils/responsive_utils.dart';
import '../providers/dashboard_riverpod_provider.dart';
import '../widgets/activity_list_widget.dart';
import '../widgets/recent_projects_widget.dart';
import '../widgets/welcome_card_widget.dart';

class DashboardRiverpodScreen extends ConsumerStatefulWidget {
  const DashboardRiverpodScreen({super.key});

  @override
  ConsumerState<DashboardRiverpodScreen> createState() =>
      _DashboardRiverpodScreenState();
}

class _DashboardRiverpodScreenState
    extends ConsumerState<DashboardRiverpodScreen> {
  @override
  void initState() {
    super.initState();
    // Charger les données au démarrage avec un délai minimal pour éviter les blocages UI
    Future.microtask(() {
      // Vérifier si les données sont déjà chargées pour éviter des requêtes inutiles
      final state = ref.read(dashboardProvider);
      if (state.isLoading ||
          (!state.hasError && state.recentProjects.isNotEmpty)) {
        return; // Données déjà chargées ou en cours de chargement
      }
      ref.read(dashboardProvider.notifier).loadDashboardData();
    });
  }

  // Méthode pour rafraîchir les données
  Future<void> _refreshData() async {
    // Utiliser la méthode existante pour rafraîchir les données
    await ref.read(dashboardProvider.notifier).loadDashboardData();

    // Afficher un message de confirmation
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Données rafraîchies'),
          duration: Duration(seconds: 1),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final dashboardState = ref.watch(dashboardProvider);

    return Scaffold(
      backgroundColor: theme.colorScheme
          .surface, // Utiliser la couleur du thème pour supporter le mode sombre
      appBar: AppBar(
        // Supprimer le titre pour éviter la duplication
        title: null,
        backgroundColor: theme
            .colorScheme.surface, // Utiliser la couleur du thème pour l'AppBar
        toolbarHeight: 40, // Réduire la hauteur de l'AppBar
        elevation: 0, // Supprimer l'ombre
        actions: [
          // Bouton de rafraîchissement
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: AppIconButton.ghost(
              icon: Icons.refresh,
              onPressed: _refreshData,
              tooltip: 'Rafraîchir',
              size: AppIconButtonSize.small,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
      body: dashboardState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : dashboardState.hasError
              ? _buildErrorView(theme, dashboardState.errorMessage)
              : _buildDashboardContent(context, dashboardState),
    );
  }

  // Affichage en cas d'erreur
  Widget _buildErrorView(ThemeData theme, String? errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 48,
          ),
          const SizedBox(height: 16),
          Text(
            'Erreur lors du chargement des données',
            style: theme.textTheme.titleMedium?.copyWith(
              letterSpacing: -0.3,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            errorMessage ?? 'Une erreur inconnue est survenue',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              letterSpacing: -0.2,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          AppButton.primary(
            onPressed: _refreshData,
            text: 'Réessayer',
            icon: Icons.refresh,
            size: AppButtonSize.medium,
          ),
        ],
      ),
    );
  }

  // Contenu principal du dashboard
  Widget _buildDashboardContent(BuildContext context, DashboardState state) {
    // Utiliser les utilitaires responsive pour déterminer le padding
    final theme = Theme.of(context);
    final isMobile = ResponsiveUtils.isMobile(context);
    final horizontalPadding = ResponsiveUtils.getHorizontalPadding(context);
    final verticalPadding = ResponsiveUtils.getVerticalPadding(context);

    return RefreshIndicator(
      onRefresh: _refreshData,
      color: theme.colorScheme.primary,
      backgroundColor: theme.colorScheme.surface,
      strokeWidth: 2.5,
      displacement: 20,
      child: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding,
          vertical: verticalPadding,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment
              .stretch, // Étirer les widgets pour prendre toute la largeur
          children: [
            // Carte de bienvenue
            WelcomeCardWidget(
              userName: state.userName ?? 'Utilisateur',
              projectCount: state.totalProjects,
              pendingTasksCount: state.pendingTasksCount,
            ),

            SizedBox(
                height: isMobile ? 24 : 32), // Augmenté de 20 à 24 pour mobile

            // Layout style Bento (grille modulaire)
            _buildBentoGrid(context, state),
          ],
        ),
      ),
    );
  }

  // Méthode pour construire la grille style Bento
  Widget _buildBentoGrid(BuildContext context, DashboardState state) {
    final isMobile = ResponsiveUtils.isMobile(context);
    final isTablet = ResponsiveUtils.isTablet(context);
    final spacing = ResponsiveUtils.getSpacing(context);

    if (isMobile) {
      // Layout mobile: utiliser des onglets pour économiser de l'espace vertical
      return DefaultTabController(
        length: 3,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Statistiques en grille (toujours visibles)
            _buildStatCards(context, state),

            const SizedBox(height: 16),

            // TabBar pour naviguer entre les sections
            TabBar(
              tabs: const [
                Tab(text: 'Projets'),
                Tab(text: 'Activités'),
                Tab(text: 'Tous'),
              ],
              labelColor: Theme.of(context).colorScheme.primary,
              unselectedLabelColor:
                  Theme.of(context).colorScheme.onSurfaceVariant,
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: Colors.transparent,
            ),

            const SizedBox(height: 16),

            // Contenu des onglets
            SizedBox(
              height: 400, // Hauteur fixe pour éviter les problèmes de layout
              child: TabBarView(
                children: [
                  // Onglet Projets
                  SingleChildScrollView(
                    child: _buildRecentProjects(context, state),
                  ),

                  // Onglet Activités
                  SingleChildScrollView(
                    child: _buildRecentActivities(context, state),
                  ),

                  // Onglet Tous (affiche les deux)
                  SingleChildScrollView(
                    child: Column(
                      children: [
                        _buildRecentProjects(context, state),
                        const SizedBox(height: 24),
                        _buildRecentActivities(context, state),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    } else if (isTablet) {
      // Layout tablette: statistiques en ligne, puis projets et activités en colonnes
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Statistiques en ligne
          SizedBox(
            height: 100, // Hauteur fixe pour les cartes de statistiques
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                SizedBox(
                  width: 280,
                  child: _buildStatCard(
                    context,
                    'Projets',
                    state.totalProjects.toString(),
                    Icons.folder_special,
                    const Color(0xFF3B82F6),
                    state.projectsTrend,
                    state.projectsTrendPositive,
                    () => context.go('/projects'),
                  ),
                ),
                const SizedBox(width: 16),
                SizedBox(
                  width: 280,
                  child: _buildStatCard(
                    context,
                    'Audits',
                    state.totalAudits.toString(),
                    Icons.assignment,
                    const Color(0xFF10B981),
                    state.auditsTrend,
                    state.auditsTrendPositive,
                    () => context.go('/audit/list'),
                  ),
                ),
                const SizedBox(width: 16),
                SizedBox(
                  width: 280,
                  child: _buildStatCard(
                    context,
                    'Rapports',
                    state.totalReports.toString(),
                    Icons.description,
                    const Color(0xFF8B5CF6),
                    state.reportsTrend,
                    state.reportsTrendPositive,
                    () => context.go('/projects'),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Projets et activités en ligne
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 3,
                child: _buildRecentProjects(context, state),
              ),
              const SizedBox(width: 24),
              Expanded(
                flex: 2,
                child: _buildRecentActivities(context, state),
              ),
            ],
          ),
        ],
      );
    } else {
      // Layout desktop: vrai style Bento avec grille asymétrique
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Première rangée: 3 cartes de statistiques côte à côte
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  context,
                  'Projets',
                  state.totalProjects.toString(),
                  Icons.folder_special,
                  const Color(0xFF3B82F6),
                  state.projectsTrend,
                  state.projectsTrendPositive,
                  () => context.go('/projects'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Audits',
                  state.totalAudits.toString(),
                  Icons.assignment,
                  const Color(0xFF10B981),
                  state.auditsTrend,
                  state.auditsTrendPositive,
                  () => context.go('/audit/list'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Rapports',
                  state.totalReports.toString(),
                  Icons.description,
                  const Color(0xFF8B5CF6),
                  state.reportsTrend,
                  state.reportsTrendPositive,
                  () => context.go('/projects'),
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Deuxième rangée: projets récents et activités récentes
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 3,
                child: _buildRecentProjects(context, state),
              ),
              const SizedBox(width: 24),
              Expanded(
                flex: 2,
                child: _buildRecentActivities(context, state),
              ),
            ],
          ),
        ],
      );
    }
  }

  // Méthode pour construire les cartes de statistiques
  Widget _buildStatCards(BuildContext context, DashboardState state) {
    final crossAxisCount = ResponsiveUtils.getGridColumns(
      context,
      mobileColumns: 1,
      tabletColumns: 2,
      desktopColumns: 3,
    );
    final spacing = ResponsiveUtils.getSpacing(context);
    final isMobile = ResponsiveUtils.isMobile(context);

    return GridView.count(
      crossAxisCount: crossAxisCount,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: spacing,
      mainAxisSpacing: spacing,
      childAspectRatio: isMobile ? 5.0 : 3.2, // Ratio plus large sur mobile
      children: [
        _buildStatCard(
          context,
          'Projets',
          state.totalProjects.toString(),
          Icons.folder_special,
          const Color(0xFF3B82F6),
          state.projectsTrend,
          state.projectsTrendPositive,
          () => context.go('/projects'), // Route vers la liste des projets
        ),
        _buildStatCard(
          context,
          'Audits',
          state.totalAudits.toString(),
          Icons.assignment,
          const Color(0xFF10B981),
          state.auditsTrend,
          state.auditsTrendPositive,
          () => context.go('/audit/list'), // Route vers la liste des audits
        ),
        _buildStatCard(
          context,
          'Rapports',
          state.totalReports.toString(),
          Icons.description,
          const Color(0xFF8B5CF6),
          state.reportsTrend,
          state.reportsTrendPositive,
          () => context.go(
              '/projects'), // Redirection vers les projets pour accéder aux rapports
        ),
      ],
    );
  }

  // Méthode pour construire les projets récents
  Widget _buildRecentProjects(BuildContext context, DashboardState state) {
    return RecentProjectsWidget(
      projects: state.recentProjects,
      title: 'Projets récents',
      onViewAll: () =>
          context.go('/projects'), // Route vers la liste des projets
      onProjectTap: (project) => context.go(
          '/project/detail/${project.projectId}'), // Route vers le détail du projet
    );
  }

  // Méthode pour construire les activités récentes
  Widget _buildRecentActivities(BuildContext context, DashboardState state) {
    return ActivityListWidget(
      activities: state.recentActivities,
      title: 'Activités récentes',
      onViewAll: () => context.go('/'), // Redirection vers la page principale
    );
  }

  // Méthode pour créer une carte de statistiques compacte style Notion
  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
    String trend,
    bool trendPositive,
    VoidCallback? onTap,
  ) {
    final theme = Theme.of(context);

    // Contenu de la carte avec la même structure que précédemment
    Widget cardContent = Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Partie gauche: icône et titre
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.12),
                borderRadius: PlatformUtils.getBorderRadius(),
                border: Border.all(
                  color: color.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Icon(
                icon,
                color: color,
                size: ResponsiveUtils.getIconSize(context),
              ),
            ),
            SizedBox(
                width: ResponsiveUtils.getSpacing(context, isLarge: false)),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    letterSpacing: -0.3,
                    fontSize: MediaQuery.of(context).size.width < 600
                        ? 12
                        : 13, // Taille réduite sur mobile pour plus de compacité
                  ),
                ),
                SizedBox(
                    height: MediaQuery.of(context).size.width < 600
                        ? 2
                        : 4), // Espacement réduit sur mobile
                Text(
                  value,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    letterSpacing: -0.5,
                    fontSize: MediaQuery.of(context).size.width < 600
                        ? 14
                        : 16, // Taille réduite sur mobile
                  ),
                ),
              ],
            ),
          ],
        ),

        // Partie droite: tendance et flèche
        Row(
          children: [
            _buildTrendBadge(theme, trend, trendPositive),
            SizedBox(
                width: MediaQuery.of(context).size.width < 600
                    ? 4
                    : 8), // Espacement réduit sur mobile
            Icon(
              Icons.arrow_forward,
              size: 14,
              color: theme.colorScheme.onSurfaceVariant.withAlpha(150),
            ),
          ],
        ),
      ],
    );

    // Utiliser AppCard au lieu de Container+Material+InkWell
    // Si onTap est null, utiliser une version non cliquable
    if (onTap == null) {
      return AppCard(
        variant: AppCardVariant.filled,
        elevation: 0,
        color: theme.colorScheme.surface,
        padding: EdgeInsets.symmetric(
            horizontal: MediaQuery.of(context).size.width < 600 ? 12 : 16,
            vertical: MediaQuery.of(context).size.width < 600
                ? 10
                : 12), // Padding réduit sur mobile pour plus de compacité
        borderRadius: BorderRadius.circular(10),
        hoverEffect: true,
        child: cardContent,
      );
    }

    // Sinon, utiliser la version cliquable
    return AppCard.clickable(
      onTap: onTap,
      variant: AppCardVariant.filled,
      elevation: 0,
      color: theme.colorScheme.surface,
      padding: EdgeInsets.symmetric(
          horizontal: MediaQuery.of(context).size.width < 600 ? 12 : 16,
          vertical: MediaQuery.of(context).size.width < 600
              ? 10
              : 12), // Padding réduit sur mobile pour plus de compacité
      borderRadius: BorderRadius.circular(10),
      hoverEffect: true,
      child: cardContent,
    );
  }

  // Badge de tendance avec animation
  Widget _buildTrendBadge(ThemeData theme, String trend, bool trendPositive) {
    // Utiliser les couleurs du thème pour plus de cohérence
    final IconData trendIcon =
        trendPositive ? Icons.arrow_upward : Icons.arrow_downward;

    // Utiliser AppBadge avec la variante appropriée
    return trendPositive
        ? AppBadge.success(
            text: trend,
            icon: trendIcon,
            size: AppBadgeSize.small, // Taille réduite pour plus de compacité
          )
        : AppBadge.destructive(
            text: trend,
            icon: trendIcon,
            size: AppBadgeSize.small, // Taille réduite pour plus de compacité
          );
  }
}
