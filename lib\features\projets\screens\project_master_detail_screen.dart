import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:seqqo/core/widgets/multi_select_dropdown.dart';
import 'package:seqqo/features/projets/constants/project_constants.dart';
import 'package:seqqo/features/projets/models/project_data.dart';
import 'package:seqqo/features/projets/widgets/paginated_project_list_widget.dart';
import 'package:seqqo/features/projets/services/project_storage_service.dart';
import 'package:seqqo/features/clients/services/client_storage_service.dart';
import 'package:intl/intl.dart';
import 'package:seqqo/core/widgets/segmented_button.dart';
import 'package:seqqo/core/widgets/app_button.dart';
import 'package:seqqo/core/widgets/app_card.dart';
import 'package:seqqo/core/widgets/app_text_field.dart';
import 'package:seqqo/core/widgets/mobile_components/mobile_fab.dart';
import 'package:seqqo/core/utils/platform_utils.dart';
import 'package:seqqo/core/utils/responsive_utils.dart';
import 'package:seqqo/features/projets/widgets/project_edit_dialog.dart';
import 'package:seqqo/features/projets/screens/internal/project_detail_screen_wrapper.dart';

// Extension pour ajouter les propriétés manquantes à ProjectData
extension ProjectDataExtensions on ProjectData {
  String get clientName => clientId; // Utiliser clientId temporairement

  String get updatedAt {
    try {
      final date = createdAt?.toDate() ?? DateTime.now();
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return 'Date inconnue';
    }
  }
}

class ProjectMasterDetailScreen extends ConsumerStatefulWidget {
  final void Function(String projectId)? onProjectSelected;
  const ProjectMasterDetailScreen({super.key, this.onProjectSelected});

  @override
  ConsumerState<ProjectMasterDetailScreen> createState() =>
      _ProjectMasterDetailScreenState();
}

class _ProjectMasterDetailScreenState
    extends ConsumerState<ProjectMasterDetailScreen> {
  final Logger _logger = Logger('ProjectMasterDetailScreen');
  final List<ProjectData> _allProjects = []; // Tous les projets non filtrés
  List<ProjectData> _filteredProjects = []; // Projets après filtrage
  ProjectData? _selectedProject;
  bool _isLoading = true;
  final ProjectStorageService _projectService = ProjectStorageService();
  final ClientStorageService _clientService = ClientStorageService();
  Map<String, String> _clientNames =
      {}; // Map pour stocker les noms des clients

  // Contrôles pour la recherche et le filtrage
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  List<String> _selectedStatuses =
      []; // Liste des statuts sélectionnés pour le filtrage
  String _sortBy = 'Date'; // Options: 'Date', 'Nom', 'Client'

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterProjects() {
    setState(() {
      _filteredProjects = _allProjects.where((project) {
        final matchesSearch = _searchQuery.isEmpty ||
            (project.projectName
                    ?.toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ??
                false) ||
            (_clientNames[project.clientId]
                    ?.toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ??
                false);

        if (_selectedStatuses.isEmpty) {
          return matchesSearch;
        }

        bool matchesStatus = false;
        for (final status in _selectedStatuses) {
          String dbStatus;
          if (ProjectStatus.displayValues.contains(status)) {
            dbStatus = ProjectStatus.fromDisplay(status);
          } else {
            final Map<String, String> statusMap = {
              'Esquisse': 'esquisse',
              'Étude / Audit': 'etude_audit',
              'Avant-Projet Sommaire': 'aps',
              'Conception': 'conception',
              'Avant-Projet Définitif': 'apd',
              'Dossiers Admin': 'dossiers_admin',
              'Dossier de Consultation': 'dce',
              'Analyse des Offres': 'analyse_offres',
              'Suivi de Chantier': 'suivi_chantier',
              'Exécution': 'execution',
              'Réception': 'reception',
              'Prépa Chantier': 'prepa_chantier',
              'Livraison': 'livraison',
              'Annulé': 'annule',
            };
            dbStatus =
                statusMap[status] ?? status.toLowerCase().replaceAll(' ', '_');
          }
          if (project.status == dbStatus || project.status == status) {
            matchesStatus = true;
            break;
          }
        }
        return matchesSearch && matchesStatus;
      }).toList();

      _sortProjects();
    });
  }

  void _sortProjects() {
    switch (_sortBy) {
      case 'Date':
        _filteredProjects.sort((a, b) {
          final dateA = a.createdAt?.toDate() ?? DateTime(0);
          final dateB = b.createdAt?.toDate() ?? DateTime(0);
          return dateB.compareTo(dateA);
        });
        break;
      case 'Nom':
        _filteredProjects.sort(
            (a, b) => (a.projectName ?? '').compareTo(b.projectName ?? ''));
        break;
      case 'Client':
        _filteredProjects.sort((a, b) => (_clientNames[a.clientId] ?? '')
            .compareTo(_clientNames[b.clientId] ?? ''));
        break;
    }
  }

  Future<void> _loadProjects() async {
    setState(() => _isLoading = true);
    try {
      final clients = await _clientService.listClients();
      final clientNameMap = {
        for (var client in clients)
          client.clientId: client.name ?? 'Client inconnu'
      };
      final projects = await _projectService.listProjects();

      if (mounted) {
        setState(() {
          _allProjects.clear();
          _allProjects.addAll(projects);
          _clientNames = clientNameMap;
          _isLoading = false;
          _filterProjects();
        });
      }
    } catch (e, s) {
      _logger.severe('Erreur lors du chargement des projets ou clients', e, s);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement des projets: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildProjectListPane(BuildContext context) {
    return Column(
      children: [
        _buildSearchHeader(),
        Expanded(
          child: Padding(
            padding:
                const EdgeInsets.only(left: 16.0, right: 16.0, bottom: 16.0),
            child: PaginatedProjectListWidget(
              projects: _filteredProjects,
              selectedProject: _selectedProject,
              clientNames: _clientNames,
              onProjectSelected: (project) {
                if (widget.onProjectSelected != null) {
                  widget.onProjectSelected!(project.projectId);
                } else {
                  // Utiliser ResponsiveUtils pour détecter les écrans mobiles/tablettes
                  if (ResponsiveUtils.isSmallScreen(context)) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => ProjectDetailScreenWrapper(
                          projectId: project.projectId,
                        ),
                      ),
                    );
                  }
                }
                setState(() {
                  _selectedProject = project;
                });
              },
              onDeleteProject: (project) async {
                if (await _confirmDeleteProject(project)) {
                  _loadProjects();
                }
              },
              onOpenFullPage: (project) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => ProjectDetailScreenWrapper(
                      projectId: project.projectId,
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Future<bool> _confirmDeleteProject(ProjectData project) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: Text(
            'Êtes-vous sûr de vouloir supprimer le projet "${project.projectName}" ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _projectService.deleteProject(project.projectId);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Projet supprimé avec succès')),
          );
        }
        return true;
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Erreur lors de la suppression: $e')),
          );
        }
        return false;
      }
    }
    return false;
  }

  Widget _buildSearchHeader() {
    final theme = Theme.of(context);
    final isMobile = ResponsiveUtils.isMobile(context);

    final sortSegments = [
      const ButtonSegment<String>(
        value: 'Date',
        label: Text('Date', style: TextStyle(fontSize: 14)),
        icon: Icon(Icons.calendar_today_outlined, size: 18),
      ),
      const ButtonSegment<String>(
        value: 'Nom',
        label: Text('Nom', style: TextStyle(fontSize: 14)),
        icon: Icon(Icons.sort_by_alpha, size: 18),
      ),
      const ButtonSegment<String>(
        value: 'Client',
        label: Text('Client', style: TextStyle(fontSize: 14)),
        icon: Icon(Icons.business, size: 18),
      ),
    ];

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(
          top: 12.0, left: 16.0, right: 16.0, bottom: 8.0),
      padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
      decoration: BoxDecoration(
        border: Border(
            bottom: BorderSide(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
                width: 1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Tooltip(
                  message: 'Rechercher des projets par nom ou client',
                  child: AppTextField(
                    controller: _searchController,
                    label: '',
                    hint: 'Rechercher par nom, client...',
                    prefixIcon: const Icon(Icons.search, size: 18),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.close, size: 16),
                            onPressed: () {
                              setState(() {
                                _searchController.clear();
                                _searchQuery = '';
                                _filterProjects();
                              });
                            },
                          )
                        : null,
                    variant: AppTextFieldVariant.outlined,
                    size: AppTextFieldSize.small,
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                        _filterProjects();
                      });
                    },
                  ),
                ),
              ),
              const SizedBox(width: 12),
              AppButton.icon(
                icon: Icons.refresh,
                onPressed: _loadProjects,
                type: AppButtonType.ghost,
                size: AppButtonSize.small,
                tooltip: 'Rafraîchir la liste des projets',
              ),
              if (!isMobile) ...[
                const SizedBox(width: 8),
                AppButton.primary(
                  icon: Icons.add,
                  text: 'Nouveau',
                  size: AppButtonSize.small,
                  tooltip: 'Créer un nouveau projet',
                  onPressed: () => _showProjectEditDialog(),
                ),
              ],
            ],
          ),
          const SizedBox(height: 16),
          isMobile
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildStatusFilter(theme),
                    const SizedBox(height: 16),
                    _buildSortFilter(theme, sortSegments),
                  ],
                )
              : Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(flex: 3, child: _buildStatusFilter(theme)),
                    const SizedBox(width: 16),
                    Expanded(
                        flex: 2, child: _buildSortFilter(theme, sortSegments)),
                  ],
                ),
          const SizedBox(height: 16),
          _buildStatsRow(isMobile),
        ],
      ),
    );
  }

  Widget _buildStatusFilter(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Statut', style: theme.textTheme.labelLarge),
        const SizedBox(height: 6),
        Tooltip(
          message: 'Filtrer les projets par statut',
          child: SizedBox(
            width: double.infinity,
            child: MultiSelectDropdown<String>(
              options: _getProjectStatusOptions(),
              selectedValues: _selectedStatuses,
              onSelectionChanged: (List<String> selection) {
                setState(() {
                  _selectedStatuses = selection;
                  _filterProjects();
                });
              },
              placeholder: 'Filtrer par statut',
              showSelectAll: true,
              showClearAll: true,
              selectAllText: 'Tous les statuts',
              clearAllText: 'Aucun statut',
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSortFilter(
      ThemeData theme, List<ButtonSegment<String>> sortSegments) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Trier par', style: theme.textTheme.labelLarge),
        const SizedBox(height: 6),
        Tooltip(
          message: 'Trier les projets',
          child: AppSegmentedButton<String>(
            segments: sortSegments,
            selected: {_sortBy},
            isCompact: false,
            isMinimal: true,
            onSelectionChanged: (Set<String> selection) {
              setState(() {
                _sortBy = selection.first;
                _sortProjects();
              });
            },
          ),
        ),
      ],
    );
  }

  List<MultiSelectOption<String>> _getProjectStatusOptions() {
    return [
      MultiSelectOption(
          value: 'Nouveau',
          label: 'Nouveau',
          icon: Icons.fiber_new_outlined,
          iconColor: Colors.orange),
      MultiSelectOption(
          value: 'Esquisse',
          label: 'Esquisse',
          icon: Icons.draw,
          iconColor: Colors.indigo.shade300),
      MultiSelectOption(
          value: 'Étude / Audit',
          label: 'Étude / Audit',
          icon: Icons.search,
          iconColor: Colors.blue),
      MultiSelectOption(
          value: 'Avant-Projet Sommaire',
          label: 'Avant-Projet Sommaire',
          icon: Icons.description_outlined,
          iconColor: Colors.teal.shade300),
      MultiSelectOption(
          value: 'Conception',
          label: 'Conception',
          icon: Icons.architecture,
          iconColor: Colors.purple),
      MultiSelectOption(
          value: 'Avant-Projet Définitif',
          label: 'Avant-Projet Définitif',
          icon: Icons.article_outlined,
          iconColor: Colors.deepPurple.shade300),
      MultiSelectOption(
          value: 'Dossiers Admin',
          label: 'Dossiers Admin',
          icon: Icons.folder_special,
          iconColor: Colors.amber.shade700),
      MultiSelectOption(
          value: 'Dossier de Consultation',
          label: 'Dossier de Consultation',
          icon: Icons.folder_copy_outlined,
          iconColor: Colors.amber.shade500),
      MultiSelectOption(
          value: 'Analyse des Offres',
          label: 'Analyse des Offres',
          icon: Icons.analytics_outlined,
          iconColor: Colors.lightBlue.shade700),
      MultiSelectOption(
          value: 'En cours',
          label: 'En cours',
          icon: Icons.pending_outlined,
          iconColor: Colors.green),
      MultiSelectOption(
          value: 'Suivi de Chantier',
          label: 'Suivi de Chantier',
          icon: Icons.build_outlined,
          iconColor: Colors.green.shade700),
      MultiSelectOption(
          value: 'Exécution',
          label: 'Exécution',
          icon: Icons.construction,
          iconColor: Colors.red),
      MultiSelectOption(
          value: 'Réception',
          label: 'Réception',
          icon: Icons.check_circle_outline,
          iconColor: Colors.lightGreen.shade700),
      MultiSelectOption(
          value: 'Terminé',
          label: 'Terminé',
          icon: Icons.task_alt,
          iconColor: Colors.blue),
      MultiSelectOption(
          value: 'Prépa Chantier',
          label: 'Prépa Chantier',
          icon: Icons.engineering,
          iconColor: Colors.orange),
      MultiSelectOption(
          value: 'Livraison',
          label: 'Livraison',
          icon: Icons.check_circle,
          iconColor: Colors.green),
      MultiSelectOption(
          value: 'Annulé',
          label: 'Annulé',
          icon: Icons.cancel,
          iconColor: Colors.grey),
    ];
  }

  Widget _buildStatsRow(bool isMobile) {
    if (isMobile) {
      // Sur mobile: chips compacts horizontaux
      return SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            _buildProjectStatChip(
              context,
              Icons.folder,
              _allProjects.length,
              'Projets',
              const Color(0xFF3B82F6),
            ),
            const SizedBox(width: 8),
            _buildProjectStatChip(
              context,
              Icons.fiber_new,
              _allProjects.where((p) => p.status == 'nouveau').length,
              'Nouveaux',
              Colors.orange.shade600,
            ),
            const SizedBox(width: 8),
            _buildProjectStatChip(
              context,
              Icons.construction,
              _allProjects.where((p) => p.status == 'en_cours').length,
              'En cours',
              Colors.amber.shade600,
            ),
            const SizedBox(width: 8),
            _buildProjectStatChip(
              context,
              Icons.check_circle,
              _allProjects.where((p) => p.status == 'termine').length,
              'Terminés',
              Colors.purple.shade600,
            ),
          ],
        ),
      );
    }

    // Sur desktop: cartes complètes
    final statsCards = [
      AppCard.info(
          icon: Icons.folder,
          title: _allProjects.length.toString(),
          subtitle: 'Projets',
          size: AppCardSize.small,
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
          child: const SizedBox.shrink()),
      AppCard.success(
          icon: Icons.fiber_new_outlined,
          title: _allProjects
              .where((p) => p.status == 'nouveau')
              .length
              .toString(),
          subtitle: 'Nouveaux',
          size: AppCardSize.small,
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
          child: const SizedBox.shrink()),
      AppCard.warning(
          icon: Icons.construction,
          title: _allProjects
              .where((p) => p.status == 'en_cours')
              .length
              .toString(),
          subtitle: 'En cours',
          size: AppCardSize.small,
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
          child: const SizedBox.shrink()),
      AppCard(
          icon: Icons.check_circle_outline,
          title: _allProjects
              .where((p) => p.status == 'termine')
              .length
              .toString(),
          subtitle: 'Terminés',
          iconColor: Colors.purple.shade700,
          size: AppCardSize.small,
          variant: AppCardVariant.colored,
          color: Colors.purple.shade700.withValues(alpha: 0.08),
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
          child: const SizedBox.shrink()),
    ];

    return Row(
        children: statsCards
            .map((card) => Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4.0),
                    child: card,
                  ),
                ))
            .toList());
  }

  // Widget pour afficher un chip de statistique compact (mobile) pour les projets
  Widget _buildProjectStatChip(BuildContext context, IconData icon, int count,
      String label, Color color) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            '$count',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
          const SizedBox(width: 2),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  void _showProjectEditDialog() {
    showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel: 'Nouveau projet',
      barrierColor: Colors.black54,
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) =>
          ProjectEditDialog(
        onProjectSaved: () => _loadProjects(),
      ),
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutCubic,
            ),
            child: child,
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: Stack(
          children: [
            _buildProjectListPane(context),
            if (_isLoading)
              Container(
                color: Theme.of(context).colorScheme.surface.withAlpha(200),
                child: Center(
                  child: CircularProgressIndicator(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
          ],
        ),
      ),
      floatingActionButton: ResponsiveUtils.isMobile(context)
          ? MobileExtendedFloatingActionButton(
              onPressed: () => _showProjectEditDialog(),
              icon: const Icon(Icons.add),
              label: const Text('Nouveau projet'),
              tooltip: 'Créer un nouveau projet',
            )
          : null,
    );
  }
}
