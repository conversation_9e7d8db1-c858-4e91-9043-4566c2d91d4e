import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/company_repository.dart';
import '../models/company_model.dart';
import 'package:seqqo/core/widgets/modern_modal.dart';
import 'package:seqqo/core/widgets/app_button.dart';
import 'package:seqqo/core/utils/responsive_utils.dart';

class CompanyFormScreen extends ConsumerStatefulWidget {
  final Company? initialCompany;

  const CompanyFormScreen({super.key, this.initialCompany});

  @override
  ConsumerState<CompanyFormScreen> createState() => _CompanyFormScreenState();
}

class _CompanyFormScreenState extends ConsumerState<CompanyFormScreen> {
  final _formKey = GlobalKey<FormState>();
  late bool _isEditing;
  bool _isLoading = false;
  String? _errorMessage;

  // Form controllers
  late final TextEditingController _nameController;
  late final TextEditingController _siretController;
  late final TextEditingController _phoneNumberController;
  late final TextEditingController _emailController;
  late final TextEditingController _websiteController;
  late final TextEditingController _addressController;
  late final TextEditingController _notesController;

  // Liste des catégories sélectionnées
  List<String> _selectedCategories = [];

  // Liste des catégories prédéfinies
  final List<String> _predefinedCategories = [
    'Démolition',
    'Gros œuvre',
    'Charpente',
    'Couverture',
    'Étanchéité',
    'Menuiseries extérieures',
    'Menuiseries intérieures',
    'Plâtrerie',
    'Isolation',
    'Électricité',
    'Plomberie',
    'Chauffage',
    'Ventilation',
    'Carrelage',
    'Revêtements de sols',
    'Peinture',
    'Serrurerie',
    'Espaces verts',
    'VRD',
    'Autre',
  ];

  @override
  void initState() {
    super.initState();
    _isEditing = widget.initialCompany != null;

    // Initialiser les catégories sélectionnées si on est en mode édition
    if (_isEditing && widget.initialCompany != null) {
      _selectedCategories = List.from(widget.initialCompany!.categories);
    }

    // Initialize controllers with existing data if editing
    _nameController = TextEditingController(
      text: widget.initialCompany?.name ?? '',
    );
    _siretController = TextEditingController(
      text: widget.initialCompany?.siret ?? '',
    );
    _phoneNumberController = TextEditingController(
      text: widget.initialCompany?.phoneNumber ?? '',
    );
    _emailController = TextEditingController(
      text: widget.initialCompany?.email ?? '',
    );
    _websiteController = TextEditingController(
      text: widget.initialCompany?.website ?? '',
    );
    _addressController = TextEditingController(
      text: widget.initialCompany?.address ?? '',
    );
    _notesController = TextEditingController(
      text: widget.initialCompany?.notes ?? '',
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _siretController.dispose();
    _phoneNumberController.dispose();
    _emailController.dispose();
    _websiteController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _saveCompany() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final company = Company(
        id: widget.initialCompany?.id ??
            '', // Provide empty string as default if null
        name: _nameController.text.trim(),
        siret:
            _siretController.text.isEmpty ? null : _siretController.text.trim(),
        phoneNumber: _phoneNumberController.text.isEmpty
            ? null
            : _phoneNumberController.text.trim(),
        email:
            _emailController.text.isEmpty ? null : _emailController.text.trim(),
        website: _websiteController.text.isEmpty
            ? null
            : _websiteController.text.trim(),
        address: _addressController.text.isEmpty
            ? null
            : _addressController.text.trim(),
        notes:
            _notesController.text.isEmpty ? null : _notesController.text.trim(),
        categories: _selectedCategories, // Ajout des catégories
        createdAt: widget.initialCompany?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (_isEditing) {
        await ref.read(companyRepositoryProvider).updateCompany(company);
      } else {
        await ref.read(companyRepositoryProvider).addCompany(company);
      }

      if (mounted) {
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isSmallScreen = MediaQuery.of(context).size.width < 600;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isEditing ? 'Modifier l\'entreprise' : 'Ajouter une entreprise',
        ),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          if (_isEditing)
            IconButton(
              icon: const Icon(Icons.delete_outline),
              tooltip: 'Supprimer',
              onPressed: () => _showDeleteConfirmation(context),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            // En-tête avec priorité visuelle
            if (_isEditing)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                color: theme.colorScheme.secondaryContainer,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _nameController.text,
                      style: theme.textTheme.headlineSmall?.copyWith(
                        color: theme.colorScheme.onSecondaryContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (_siretController.text.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          'SIRET: ${_siretController.text}',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSecondaryContainer
                                .withOpacity(0.8),
                          ),
                        ),
                      ),
                  ],
                ),
              ),

            // Contenu du formulaire dans un Expanded et ScrollView pour gérer le débordement
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Afficher un message d'erreur si nécessaire
                    if (_errorMessage != null)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: Material(
                          color: theme.colorScheme.errorContainer,
                          borderRadius: BorderRadius.circular(8),
                          clipBehavior: Clip.antiAlias,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  color: theme.colorScheme.onErrorContainer,
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Text(
                                    _errorMessage!,
                                    style: TextStyle(
                                      color: theme.colorScheme.onErrorContainer,
                                    ),
                                  ),
                                ),
                                IconButton(
                                  icon: Icon(
                                    Icons.close,
                                    color: theme.colorScheme.onErrorContainer,
                                  ),
                                  onPressed: () =>
                                      setState(() => _errorMessage = null),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),

                    // Section Informations Principales
                    Card(
                      margin: const EdgeInsets.only(bottom: 24),
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: theme.colorScheme.outline.withOpacity(0.2),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.business,
                                  color: theme.colorScheme.primary,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Informations Principales',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 24),

                            // Champ Nom (obligatoire)
                            TextFormField(
                              controller: _nameController,
                              decoration: InputDecoration(
                                labelText: 'Nom de l\'entreprise *',
                                hintText: 'ex: Urbay Construction',
                                filled: true,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'Le nom de l\'entreprise est obligatoire';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),

                            // Numéro SIRET (optionnel)
                            TextFormField(
                              controller: _siretController,
                              decoration: InputDecoration(
                                labelText: 'Numéro SIRET',
                                hintText: 'ex: 12345678901234',
                                suffixIcon: const Tooltip(
                                  message:
                                      'Numéro à 14 chiffres identifiant l\'entreprise',
                                  child: Icon(Icons.info_outline),
                                ),
                                filled: true,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                                LengthLimitingTextInputFormatter(14),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Section Contact
                    Card(
                      margin: const EdgeInsets.only(bottom: 24),
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: theme.colorScheme.outline.withOpacity(0.2),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.contact_phone,
                                  color: theme.colorScheme.primary,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Contact',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 24),

                            // Organisation responsive des champs sur les grands écrans
                            if (!isSmallScreen)
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: TextFormField(
                                      controller: _phoneNumberController,
                                      decoration: InputDecoration(
                                        labelText: 'Téléphone',
                                        prefixIcon: const Icon(Icons.phone),
                                        filled: true,
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                      ),
                                      keyboardType: TextInputType.phone,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: TextFormField(
                                      controller: _emailController,
                                      decoration: InputDecoration(
                                        labelText: 'Email',
                                        prefixIcon: const Icon(Icons.email),
                                        filled: true,
                                        border: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                        ),
                                      ),
                                      keyboardType: TextInputType.emailAddress,
                                      validator: (value) {
                                        if (value != null && value.isNotEmpty) {
                                          final emailRegExp = RegExp(
                                            r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                                          );
                                          if (!emailRegExp.hasMatch(value)) {
                                            return 'Format d\'email non valide';
                                          }
                                        }
                                        return null;
                                      },
                                    ),
                                  ),
                                ],
                              )
                            else
                              Column(
                                children: [
                                  // Téléphone
                                  TextFormField(
                                    controller: _phoneNumberController,
                                    decoration: InputDecoration(
                                      labelText: 'Téléphone',
                                      prefixIcon: const Icon(Icons.phone),
                                      filled: true,
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    keyboardType: TextInputType.phone,
                                  ),
                                  const SizedBox(height: 16),

                                  // Email
                                  TextFormField(
                                    controller: _emailController,
                                    decoration: InputDecoration(
                                      labelText: 'Email',
                                      prefixIcon: const Icon(Icons.email),
                                      filled: true,
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    keyboardType: TextInputType.emailAddress,
                                    validator: (value) {
                                      if (value != null && value.isNotEmpty) {
                                        final emailRegExp = RegExp(
                                          r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                                        );
                                        if (!emailRegExp.hasMatch(value)) {
                                          return 'Format d\'email non valide';
                                        }
                                      }
                                      return null;
                                    },
                                  ),
                                ],
                              ),

                            const SizedBox(height: 16),

                            // Site Web
                            TextFormField(
                              controller: _websiteController,
                              decoration: InputDecoration(
                                labelText: 'Site Web',
                                hintText: 'ex: https://www.example.com',
                                prefixIcon: const Icon(Icons.language),
                                filled: true,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              keyboardType: TextInputType.url,
                              validator: (value) {
                                if (value != null && value.isNotEmpty) {
                                  final urlRegExp = RegExp(
                                    r'^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$',
                                  );
                                  if (!urlRegExp.hasMatch(value)) {
                                    return 'Format d\'URL non valide';
                                  }
                                }
                                return null;
                              },
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Section Adresse et Notes
                    Card(
                      margin: const EdgeInsets.only(bottom: 24),
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: theme.colorScheme.outline.withOpacity(0.2),
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.location_on,
                                  color: theme.colorScheme.primary,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Adresse & Informations',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 24),

                            // Adresse
                            TextFormField(
                              controller: _addressController,
                              decoration: InputDecoration(
                                labelText: 'Adresse',
                                hintText:
                                    'ex: 123 rue des Exemples, 75000 Paris',
                                prefixIcon: const Icon(Icons.home),
                                filled: true,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Notes
                            TextFormField(
                              controller: _notesController,
                              decoration: InputDecoration(
                                labelText: 'Notes',
                                hintText:
                                    'Notes et informations supplémentaires...',
                                filled: true,
                                alignLabelWithHint: true,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              maxLines: 4,
                            ),

                            const SizedBox(height: 24),

                            // Section Catégories
                            Row(
                              children: [
                                Icon(
                                  Icons.category,
                                  color: theme.colorScheme.primary,
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  'Catégories d\'activité',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Affichage des catégories sélectionnées
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: [
                                ..._selectedCategories.map((category) {
                                  return Chip(
                                    label: Text(category),
                                    deleteIcon:
                                        const Icon(Icons.close, size: 18),
                                    onDeleted: () {
                                      setState(() {
                                        _selectedCategories.remove(category);
                                      });
                                    },
                                  );
                                }),

                                // Bouton pour ajouter une catégorie
                                ActionChip(
                                  avatar: const Icon(Icons.add),
                                  label: const Text('Ajouter'),
                                  onPressed: () =>
                                      _showCategorySelectionDialog(),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Mentions légales ou informations supplémentaires
                    Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.info_outline,
                            size: 16,
                            color: theme.colorScheme.outline,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Les champs marqués d\'un * sont obligatoires',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.outline,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Boutons de contrôle en bas toujours visibles
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.scaffoldBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 5,
                    offset: const Offset(0, -3),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed:
                        _isLoading ? null : () => Navigator.of(context).pop(),
                    child: const Text('Annuler'),
                  ),
                  const SizedBox(width: 16),
                  FilledButton(
                    onPressed: _isLoading ? null : _saveCompany,
                    child: _isLoading
                        ? SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 3,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                theme.colorScheme.onPrimary,
                              ),
                            ),
                          )
                        : Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                _isEditing ? Icons.save : Icons.add,
                                size: 18,
                              ),
                              const SizedBox(width: 8),
                              Text(_isEditing ? 'Enregistrer' : 'Ajouter'),
                            ],
                          ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Dialogue pour sélectionner une catégorie
  Future<void> _showCategorySelectionDialog() async {
    final TextEditingController customCategoryController =
        TextEditingController();
    String? selectedCategory;
    bool useCustomCategory = false;

    await showDialog<String>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Ajouter une catégorie'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Sélectionnez une catégorie :'),
                const SizedBox(height: 8),

                // Liste des catégories prédéfinies
                ...List.generate(_predefinedCategories.length, (index) {
                  final category = _predefinedCategories[index];
                  return RadioListTile<String>(
                    title: Text(category),
                    value: category,
                    groupValue: useCustomCategory ? null : selectedCategory,
                    onChanged: (value) {
                      setDialogState(() {
                        selectedCategory = value;
                        useCustomCategory = false;
                      });
                    },
                    dense: true,
                  );
                }),

                // Option pour une catégorie personnalisée
                RadioListTile<bool>(
                  title: const Text('Autre (personnalisée)'),
                  value: true,
                  groupValue: useCustomCategory,
                  onChanged: (value) {
                    setDialogState(() {
                      useCustomCategory = value!;
                    });
                  },
                  dense: true,
                ),

                if (useCustomCategory)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: TextField(
                      controller: customCategoryController,
                      decoration: const InputDecoration(
                        labelText: 'Catégorie personnalisée',
                        hintText: 'Ex: Acoustique',
                      ),
                      onChanged: (value) {
                        // La valeur est mise à jour en temps réel
                      },
                    ),
                  ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                String? finalCategory;

                if (useCustomCategory &&
                    customCategoryController.text.isNotEmpty) {
                  finalCategory = customCategoryController.text.trim();
                } else if (!useCustomCategory && selectedCategory != null) {
                  finalCategory = selectedCategory;
                }

                if (finalCategory != null) {
                  setState(() {
                    if (!_selectedCategories.contains(finalCategory)) {
                      _selectedCategories.add(finalCategory!);
                    }
                  });
                }

                Navigator.pop(context);
              },
              child: const Text('Ajouter'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showDeleteConfirmation(BuildContext context) async {
    final theme = Theme.of(context);
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            RichText(
              text: TextSpan(
                style: theme.textTheme.bodyMedium,
                children: [
                  const TextSpan(
                    text: 'Êtes-vous sûr de vouloir supprimer l\'entreprise ',
                  ),
                  TextSpan(
                    text: _nameController.text,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const TextSpan(text: ' ?'),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.errorContainer.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning_amber_rounded,
                    color: theme.colorScheme.error,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Cette action est irréversible.',
                      style: TextStyle(color: theme.colorScheme.error),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Annuler'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: FilledButton.styleFrom(
              backgroundColor: theme.colorScheme.error,
              foregroundColor: theme.colorScheme.onError,
            ),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );

    if (confirmed == true && widget.initialCompany != null) {
      setState(() {
        _isLoading = true;
      });

      try {
        await ref
            .read(companyRepositoryProvider)
            .deleteCompany(widget.initialCompany!.id);
        if (mounted) {
          Navigator.of(context).pop(true); // Return true to indicate success
        }
      } catch (e) {
        setState(() {
          _errorMessage = 'Erreur lors de la suppression: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }
}
