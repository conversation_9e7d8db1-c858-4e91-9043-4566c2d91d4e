import 'package:flutter/material.dart';
import 'package:seqqo/core/utils/platform_utils.dart';

/// Action rapide pour le FAB expandable
class QuickAction {
  final IconData icon;
  final String label;
  final VoidCallback onPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const QuickAction({
    required this.icon,
    required this.label,
    required this.onPressed,
    this.backgroundColor,
    this.foregroundColor,
  });
}

/// FAB expandable avec actions rapides modernes
class QuickActionFab extends StatefulWidget {
  final List<QuickAction> actions;
  final bool isExpanded;
  final VoidCallback onToggle;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const QuickActionFab({
    super.key,
    required this.actions,
    required this.isExpanded,
    required this.onToggle,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  State<QuickActionFab> createState() => _QuickActionFabState();
}

class _QuickActionFabState extends State<QuickActionFab>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.125, // 45 degrés (1/8 de tour)
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(QuickActionFab oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isExpanded != oldWidget.isExpanded) {
      if (widget.isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // Actions expandables
        AnimatedBuilder(
          animation: _expandAnimation,
          builder: (context, child) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: widget.actions.asMap().entries.map((entry) {
                final index = entry.key;
                final action = entry.value;
                
                // Animation décalée pour chaque action
                final delay = index * 0.1;
                final actionAnimation = Tween<double>(
                  begin: 0.0,
                  end: 1.0,
                ).animate(CurvedAnimation(
                  parent: _animationController,
                  curve: Interval(delay, 1.0, curve: Curves.easeOut),
                ));
                
                return AnimatedBuilder(
                  animation: actionAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: actionAnimation.value,
                      child: Opacity(
                        opacity: actionAnimation.value,
                        child: Container(
                          margin: const EdgeInsets.only(bottom: 12),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Label avec fond
                              if (actionAnimation.value > 0.5) ...[
                                Container(
                                  margin: const EdgeInsets.only(right: 12),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 8,
                                  ),
                                  decoration: BoxDecoration(
                                    color: theme.colorScheme.surface,
                                    borderRadius: BorderRadius.circular(8),
                                    boxShadow: [
                                      BoxShadow(
                                        color: theme.colorScheme.shadow.withValues(alpha: 0.1),
                                        offset: const Offset(0, 2),
                                        blurRadius: 4,
                                      ),
                                    ],
                                  ),
                                  child: Text(
                                    action.label,
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ],
                              
                              // Bouton d'action
                              FloatingActionButton.small(
                                onPressed: () {
                                  PlatformUtils.triggerHapticFeedback(HapticFeedbackType.light);
                                  action.onPressed();
                                  widget.onToggle(); // Fermer après action
                                },
                                backgroundColor: action.backgroundColor ?? theme.colorScheme.secondary,
                                foregroundColor: action.foregroundColor ?? theme.colorScheme.onSecondary,
                                heroTag: 'quick_action_${action.label}',
                                child: Icon(action.icon, size: 20),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              }).toList(),
            );
          },
        ),
        
        // FAB principal
        FloatingActionButton(
          onPressed: () {
            PlatformUtils.triggerHapticFeedback(HapticFeedbackType.medium);
            widget.onToggle();
          },
          backgroundColor: widget.backgroundColor ?? theme.colorScheme.primary,
          foregroundColor: widget.foregroundColor ?? theme.colorScheme.onPrimary,
          child: AnimatedBuilder(
            animation: _rotationAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotationAnimation.value * 2 * 3.14159,
                child: Icon(
                  widget.isExpanded ? Icons.close : Icons.add,
                  size: 24,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
