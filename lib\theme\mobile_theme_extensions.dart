import 'package:flutter/material.dart';
import '../core/utils/platform_utils.dart';
import '../core/utils/responsive_utils.dart';

/// Extensions de thème spécifiques pour mobile
class MobileThemeExtensions {
  /// Retourne un TextTheme optimisé pour mobile
  static TextTheme getMobileTextTheme(BuildContext context, TextTheme baseTheme) {
    if (!PlatformUtils.shouldUseMobileOptimizations(context)) {
      return baseTheme;
    }

    // Augmenter légèrement les tailles de police pour mobile
    return baseTheme.copyWith(
      // Titres plus grands pour une meilleure lisibilité
      displayLarge: baseTheme.displayLarge?.copyWith(
        fontSize: (baseTheme.displayLarge?.fontSize ?? 32) + 2,
        height: 1.2,
      ),
      displayMedium: baseTheme.displayMedium?.copyWith(
        fontSize: (baseTheme.displayMedium?.fontSize ?? 28) + 2,
        height: 1.2,
      ),
      displaySmall: baseTheme.displaySmall?.copyWith(
        fontSize: (baseTheme.displaySmall?.fontSize ?? 24) + 2,
        height: 1.2,
      ),
      
      // En-têtes optimisés
      headlineLarge: baseTheme.headlineLarge?.copyWith(
        fontSize: (baseTheme.headlineLarge?.fontSize ?? 22) + 1,
        height: 1.3,
      ),
      headlineMedium: baseTheme.headlineMedium?.copyWith(
        fontSize: (baseTheme.headlineMedium?.fontSize ?? 20) + 1,
        height: 1.3,
      ),
      headlineSmall: baseTheme.headlineSmall?.copyWith(
        fontSize: (baseTheme.headlineSmall?.fontSize ?? 18) + 1,
        height: 1.3,
      ),
      
      // Corps de texte avec meilleure lisibilité
      bodyLarge: baseTheme.bodyLarge?.copyWith(
        fontSize: 16, // Minimum 16px pour mobile
        height: 1.5,
      ),
      bodyMedium: baseTheme.bodyMedium?.copyWith(
        fontSize: 15, // Légèrement plus grand
        height: 1.5,
      ),
      bodySmall: baseTheme.bodySmall?.copyWith(
        fontSize: 13, // Minimum 13px pour mobile
        height: 1.4,
      ),
      
      // Labels plus lisibles
      labelLarge: baseTheme.labelLarge?.copyWith(
        fontSize: 15,
        height: 1.4,
      ),
      labelMedium: baseTheme.labelMedium?.copyWith(
        fontSize: 14,
        height: 1.4,
      ),
      labelSmall: baseTheme.labelSmall?.copyWith(
        fontSize: 12,
        height: 1.3,
      ),
    );
  }

  /// Retourne un ButtonThemeData optimisé pour mobile
  static ButtonThemeData getMobileButtonTheme(BuildContext context) {
    if (!PlatformUtils.shouldUseMobileOptimizations(context)) {
      return const ButtonThemeData();
    }

    return ButtonThemeData(
      minWidth: PlatformUtils.getMinTouchTargetSize(),
      height: PlatformUtils.getMinTouchTargetSize(),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      shape: RoundedRectangleBorder(
        borderRadius: PlatformUtils.getBorderRadius(),
      ),
    );
  }

  /// Retourne un ElevatedButtonThemeData optimisé pour mobile
  static ElevatedButtonThemeData getMobileElevatedButtonTheme(BuildContext context) {
    if (!PlatformUtils.shouldUseMobileOptimizations(context)) {
      return const ElevatedButtonThemeData();
    }

    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        minimumSize: Size(
          PlatformUtils.getMinTouchTargetSize() * 2,
          PlatformUtils.getMinTouchTargetSize(),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
        shape: RoundedRectangleBorder(
          borderRadius: PlatformUtils.getBorderRadius(),
        ),
        elevation: PlatformUtils.getElevation(),
        shadowColor: Colors.black.withValues(alpha: 0.2),
      ),
    );
  }

  /// Retourne un OutlinedButtonThemeData optimisé pour mobile
  static OutlinedButtonThemeData getMobileOutlinedButtonTheme(BuildContext context) {
    if (!PlatformUtils.shouldUseMobileOptimizations(context)) {
      return const OutlinedButtonThemeData();
    }

    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        minimumSize: Size(
          PlatformUtils.getMinTouchTargetSize() * 2,
          PlatformUtils.getMinTouchTargetSize(),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
        shape: RoundedRectangleBorder(
          borderRadius: PlatformUtils.getBorderRadius(),
        ),
        side: const BorderSide(width: 1.5),
      ),
    );
  }

  /// Retourne un TextButtonThemeData optimisé pour mobile
  static TextButtonThemeData getMobileTextButtonTheme(BuildContext context) {
    if (!PlatformUtils.shouldUseMobileOptimizations(context)) {
      return const TextButtonThemeData();
    }

    return TextButtonThemeData(
      style: TextButton.styleFrom(
        minimumSize: Size(
          PlatformUtils.getMinTouchTargetSize(),
          PlatformUtils.getMinTouchTargetSize(),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: PlatformUtils.getBorderRadius(),
        ),
      ),
    );
  }

  /// Retourne un ListTileThemeData optimisé pour mobile
  static ListTileThemeData getMobileListTileTheme(BuildContext context) {
    if (!PlatformUtils.shouldUseMobileOptimizations(context)) {
      return const ListTileThemeData();
    }

    return ListTileThemeData(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      minVerticalPadding: 12,
      minLeadingWidth: 32,
      horizontalTitleGap: 16,
      shape: RoundedRectangleBorder(
        borderRadius: PlatformUtils.getBorderRadius(),
      ),
      visualDensity: VisualDensity.comfortable,
    );
  }

  /// Retourne un InputDecorationTheme optimisé pour mobile
  static InputDecorationTheme getMobileInputDecorationTheme(BuildContext context) {
    if (!PlatformUtils.shouldUseMobileOptimizations(context)) {
      return const InputDecorationTheme();
    }

    return InputDecorationTheme(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      border: OutlineInputBorder(
        borderRadius: PlatformUtils.getBorderRadius(),
        borderSide: const BorderSide(width: 1.5),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: PlatformUtils.getBorderRadius(),
        borderSide: const BorderSide(width: 1.5),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: PlatformUtils.getBorderRadius(),
        borderSide: const BorderSide(width: 2.0),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: PlatformUtils.getBorderRadius(),
        borderSide: const BorderSide(width: 1.5, color: Colors.red),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: PlatformUtils.getBorderRadius(),
        borderSide: const BorderSide(width: 2.0, color: Colors.red),
      ),
      filled: true,
      isDense: false,
    );
  }

  /// Retourne un AppBarTheme optimisé pour mobile
  static AppBarTheme getMobileAppBarTheme(BuildContext context) {
    if (!PlatformUtils.shouldUseMobileOptimizations(context)) {
      return const AppBarTheme();
    }

    return AppBarTheme(
      centerTitle: PlatformUtils.isIOS,
      elevation: PlatformUtils.getElevation(),
      shadowColor: Colors.black.withValues(alpha: 0.1),
      toolbarHeight: kToolbarHeight + (PlatformUtils.isIOS ? 0 : 8),
      titleSpacing: 16,
      actionsIconTheme: const IconThemeData(size: 24),
      iconTheme: const IconThemeData(size: 24),
    );
  }

  /// Retourne un BottomNavigationBarThemeData optimisé pour mobile
  static BottomNavigationBarThemeData getMobileBottomNavigationBarTheme(BuildContext context) {
    if (!PlatformUtils.shouldUseMobileOptimizations(context)) {
      return const BottomNavigationBarThemeData();
    }

    return BottomNavigationBarThemeData(
      type: BottomNavigationBarType.fixed,
      elevation: PlatformUtils.getElevation(isHigh: true),
      selectedItemColor: Theme.of(context).colorScheme.primary,
      unselectedItemColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
      selectedLabelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
      unselectedLabelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
      showSelectedLabels: true,
      showUnselectedLabels: true,
    );
  }

  /// Retourne un NavigationBarThemeData optimisé pour mobile
  static NavigationBarThemeData getMobileNavigationBarTheme(BuildContext context) {
    if (!PlatformUtils.shouldUseMobileOptimizations(context)) {
      return const NavigationBarThemeData();
    }

    return NavigationBarThemeData(
      height: 70,
      elevation: PlatformUtils.getElevation(isHigh: true),
      shadowColor: Colors.black.withValues(alpha: 0.1),
      indicatorColor: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.7),
      indicatorShape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      labelTextStyle: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return const TextStyle(fontSize: 12, fontWeight: FontWeight.w600);
        }
        return const TextStyle(fontSize: 12, fontWeight: FontWeight.w400);
      }),
      iconTheme: WidgetStateProperty.resolveWith((states) {
        return const IconThemeData(size: 24);
      }),
    );
  }

  /// Applique toutes les optimisations mobiles à un ThemeData
  static ThemeData applyMobileOptimizations(BuildContext context, ThemeData baseTheme) {
    if (!PlatformUtils.shouldUseMobileOptimizations(context)) {
      return baseTheme;
    }

    return baseTheme.copyWith(
      textTheme: getMobileTextTheme(context, baseTheme.textTheme),
      buttonTheme: getMobileButtonTheme(context),
      elevatedButtonTheme: getMobileElevatedButtonTheme(context),
      outlinedButtonTheme: getMobileOutlinedButtonTheme(context),
      textButtonTheme: getMobileTextButtonTheme(context),
      listTileTheme: getMobileListTileTheme(context),
      inputDecorationTheme: getMobileInputDecorationTheme(context),
      appBarTheme: getMobileAppBarTheme(context),
      bottomNavigationBarTheme: getMobileBottomNavigationBarTheme(context),
      navigationBarTheme: getMobileNavigationBarTheme(context),
      visualDensity: VisualDensity.comfortable,
      materialTapTargetSize: MaterialTapTargetSize.padded,
    );
  }
}
