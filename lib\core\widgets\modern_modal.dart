import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/responsive_utils.dart';

/// Modale moderne optimisée pour mobile avec design Seqqo
class ModernModal extends StatelessWidget {
  final String title;
  final Widget child;
  final List<Widget>? actions;
  final bool isScrollable;
  final bool showCloseButton;
  final VoidCallback? onClose;
  final double? maxHeight;
  final EdgeInsets? padding;

  const ModernModal({
    super.key,
    required this.title,
    required this.child,
    this.actions,
    this.isScrollable = true,
    this.showCloseButton = true,
    this.onClose,
    this.maxHeight,
    this.padding,
  });

  /// Affiche une modale moderne avec animations
  static Future<T?> show<T>({
    required BuildContext context,
    required String title,
    required Widget child,
    List<Widget>? actions,
    bool isScrollable = true,
    bool showCloseButton = true,
    VoidCallback? onClose,
    double? maxHeight,
    EdgeInsets? padding,
    bool barrierDismissible = true,
  }) {
    return showGeneralDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierLabel: title,
      barrierColor: Colors.black.withAlpha(120),
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) => ModernModal(
        title: title,
        onClose: onClose,
        actions: actions,
        isScrollable: isScrollable,
        showCloseButton: showCloseButton,
        maxHeight: maxHeight,
        padding: padding,
        child: child,
      ),
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        final isMobile = ResponsiveUtils.isMobile(context);
        
        if (isMobile) {
          // Animation slide-up pour mobile
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 1),
              end: Offset.zero,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutCubic,
            )),
            child: child,
          );
        } else {
          // Animation scale pour desktop
          return FadeTransition(
            opacity: animation,
            child: ScaleTransition(
              scale: CurvedAnimation(
                parent: animation,
                curve: Curves.easeOutCubic,
              ),
              child: child,
            ),
          );
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isMobile = ResponsiveUtils.isMobile(context);
    final size = MediaQuery.of(context).size;

    return SafeArea(
      child: Material(
        type: MaterialType.transparency,
        child: Container(
          margin: isMobile 
              ? EdgeInsets.zero 
              : EdgeInsets.symmetric(
                  horizontal: size.width * 0.1,
                  vertical: size.height * 0.1,
                ),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: isMobile 
                ? const BorderRadius.vertical(top: Radius.circular(16))
                : BorderRadius.circular(12),
            border: Border.all(
              color: theme.colorScheme.outline.withAlpha(40),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.shadow.withAlpha(20),
                blurRadius: 16,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: isMobile ? MainAxisSize.max : MainAxisSize.min,
            children: [
              // Header avec titre et bouton fermer
              _buildHeader(context, theme, isMobile),
              
              // Contenu principal
              Expanded(
                flex: isMobile ? 1 : 0,
                child: Container(
                  constraints: BoxConstraints(
                    maxHeight: maxHeight ?? (isMobile ? double.infinity : size.height * 0.7),
                  ),
                  child: isScrollable
                      ? SingleChildScrollView(
                          padding: padding ?? const EdgeInsets.all(16),
                          child: child,
                        )
                      : Padding(
                          padding: padding ?? const EdgeInsets.all(16),
                          child: child,
                        ),
                ),
              ),
              
              // Actions en bas
              if (actions != null && actions!.isNotEmpty)
                _buildActions(context, theme, isMobile),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, ThemeData theme, bool isMobile) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 8, 8),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withAlpha(20),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                letterSpacing: -0.3,
              ),
            ),
          ),
          if (showCloseButton)
            IconButton(
              onPressed: onClose ?? () => Navigator.of(context).pop(),
              icon: const Icon(Icons.close),
              style: IconButton.styleFrom(
                backgroundColor: theme.colorScheme.surface,
                foregroundColor: theme.colorScheme.onSurface.withAlpha(150),
                minimumSize: const Size(44, 44),
              ),
              tooltip: 'Fermer',
            ),
        ],
      ),
    );
  }

  Widget _buildActions(BuildContext context, ThemeData theme, bool isMobile) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withAlpha(20),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: actions!
            .map((action) => Padding(
                  padding: const EdgeInsets.only(left: 8),
                  child: action,
                ))
            .toList(),
      ),
    );
  }
}

/// Widget de champ de formulaire moderne avec style Seqqo
class ModernFormField extends StatelessWidget {
  final String label;
  final String? hint;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final bool obscureText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final int? maxLines;
  final bool enabled;
  final VoidCallback? onTap;
  final Function(String)? onChanged;

  const ModernFormField({
    super.key,
    required this.label,
    this.hint,
    this.controller,
    this.validator,
    this.keyboardType,
    this.obscureText = false,
    this.prefixIcon,
    this.suffixIcon,
    this.maxLines = 1,
    this.enabled = true,
    this.onTap,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface.withAlpha(180),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          validator: validator,
          keyboardType: keyboardType,
          obscureText: obscureText,
          maxLines: maxLines,
          enabled: enabled,
          onTap: onTap,
          onChanged: onChanged,
          style: theme.textTheme.bodyLarge,
          decoration: InputDecoration(
            hintText: hint,
            prefixIcon: prefixIcon,
            suffixIcon: suffixIcon,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: theme.colorScheme.outline.withAlpha(60),
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: theme.colorScheme.outline.withAlpha(40),
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: theme.colorScheme.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: theme.colorScheme.error,
                width: 1,
              ),
            ),
            filled: true,
            fillColor: theme.colorScheme.surface,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }
}
