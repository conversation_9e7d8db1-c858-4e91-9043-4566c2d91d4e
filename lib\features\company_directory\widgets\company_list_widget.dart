import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:seqqo/features/company_directory/models/company_model.dart';
import 'package:seqqo/core/widgets/mobile_components/swipe_action_card.dart';
import 'package:seqqo/core/utils/responsive_utils.dart';

class CompanyListWidget extends StatefulWidget {
  final List<Company> companies;
  final Company? selectedCompany;
  final bool isLoading;
  final ValueChanged<Company> onCompanySelected;
  final ValueChanged<Company> onDeleteCompany;
  final ValueChanged<Company> onEditCompany;

  const CompanyListWidget({
    super.key,
    required this.companies,
    this.selectedCompany,
    required this.isLoading,
    required this.onCompanySelected,
    required this.onDeleteCompany,
    required this.onEditCompany,
  });

  @override
  State<CompanyListWidget> createState() => _CompanyListWidgetState();
}

class _CompanyListWidgetState extends State<CompanyListWidget> {
  int _sortColumnIndex = 0;
  bool _sortAscending = true;
  final DateFormat _dateFormat = DateFormat('dd/MM/yyyy');

  String _getInitials(String? name) {
    if (name == null || name.trim().isEmpty) return '?';
    List<String> words =
        name.trim().split(' ').where((s) => s.isNotEmpty).toList();
    if (words.isEmpty) return '?';
    if (words.length == 1) return words[0][0].toUpperCase();
    return (words[0][0] + words[1][0]).toUpperCase();
  }

  // Widget pour afficher une catégorie avec le style des projets
  Widget _buildCategoryChip(String category, ThemeData theme) {
    // Couleur basée sur le hash de la catégorie pour cohérence
    final colorIndex = category.hashCode.abs() % 6;
    final colors = [
      Colors.blue.shade600,
      Colors.green.shade600,
      Colors.orange.shade600,
      Colors.purple.shade600,
      Colors.teal.shade600,
      Colors.red.shade600,
    ];
    final statusColor = colors[colorIndex];

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        color: statusColor.withAlpha(20),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: statusColor.withAlpha(60), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.category,
            size: 12,
            color: statusColor,
          ),
          const SizedBox(width: 4),
          Text(
            category,
            style: TextStyle(
              color: statusColor,
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isMobile = ResponsiveUtils.isMobile(context);

    if (widget.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (widget.companies.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.business_outlined,
                size: 64, color: Theme.of(context).colorScheme.outline),
            const SizedBox(height: 16),
            Text('Aucune entreprise trouvée',
                style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 8),
            Text(
              'Ajoutez une entreprise pour la voir ici.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      );
    }

    return isMobile
        ? _buildMobileList(widget.companies)
        : _buildDesktopTable(widget.companies);
  }

  Widget _buildMobileList(List<Company> companies) {
    final theme = Theme.of(context);
    return ListView.builder(
      itemCount: companies.length,
      padding: const EdgeInsets.all(8),
      itemBuilder: (context, index) {
        final company = companies[index];
        final isSelected = widget.selectedCompany?.id == company.id;
        return _buildMobileCompanyListItem(company, isSelected, theme);
      },
    );
  }

  Widget _buildMobileCompanyListItem(
      Company company, bool isSelected, ThemeData theme) {
    // Wrapper avec actions de swipe pour mobile
    Widget cardContent = Card(
      elevation: 0,
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12), // Seqqo border radius
        side: BorderSide(
          color: isSelected
              ? theme.colorScheme.primary.withAlpha(100)
              : theme.colorScheme.outline.withAlpha(40),
          width: 1,
        ),
      ),
      color: isSelected
          ? theme.colorScheme.primary.withAlpha(15)
          : theme.colorScheme.surface,
      child: InkWell(
        onTap: () {
          HapticFeedback.lightImpact();
          widget.onCompanySelected(company);
        },
        borderRadius: BorderRadius.circular(12),
        hoverColor: theme.colorScheme.primary.withAlpha(10),
        child: Padding(
          padding: const EdgeInsets.all(16.0), // Augmenté pour mobile
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Première ligne: Nom de l'entreprise et catégorie principale (style projets)
              Row(
                children: [
                  Icon(
                    Icons.business,
                    size: 14,
                    color: isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface.withAlpha(150),
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      company.name,
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.w500,
                        color: isSelected
                            ? theme.colorScheme.primary
                            : theme.colorScheme.onSurface,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 6),
                  // Catégorie principale avec style Notion
                  if (company.categories.isNotEmpty)
                    _buildCategoryChip(company.categories.first, theme),
                ],
              ),

              const SizedBox(height: 8),

              // Deuxième ligne: SIRET
              if (company.siret?.isNotEmpty ?? false)
                Row(
                  children: [
                    Icon(
                      Icons.tag,
                      size: 14,
                      color: theme.colorScheme.onSurface.withAlpha(120),
                    ),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        'SIRET: ${company.siret}',
                        style: TextStyle(
                          fontSize: 12,
                          color: theme.colorScheme.onSurface.withAlpha(180),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),

              const SizedBox(height: 8),

              // Troisième ligne: Contact
              if (company.email != null || company.phoneNumber != null)
                Row(
                  children: [
                    Icon(
                      Icons.contact_mail_outlined,
                      size: 14,
                      color: theme.colorScheme.onSurface.withAlpha(120),
                    ),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        [company.email, company.phoneNumber]
                            .where((e) => e != null && e.isNotEmpty)
                            .join(' • '),
                        style: TextStyle(
                          fontSize: 12,
                          color: theme.colorScheme.onSurface.withAlpha(180),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),

              const SizedBox(height: 8),

              // Quatrième ligne: Date et indicateur de swipe (style projets)
              Row(
                children: [
                  Icon(
                    Icons.calendar_today_outlined,
                    size: 14,
                    color: theme.colorScheme.onSurface.withAlpha(120),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    _dateFormat.format(company.createdAt),
                    style: TextStyle(
                      fontSize: 11,
                      color: theme.colorScheme.onSurface.withAlpha(150),
                    ),
                  ),
                  const Spacer(),
                  // Indicateur de swipe sur mobile
                  Icon(
                    Icons.more_horiz,
                    size: 16,
                    color: theme.colorScheme.onSurface.withAlpha(120),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );

    // Retourner avec actions de swipe sur mobile
    return SwipeActionCard(
      longPressTitle: 'Actions pour ${company.name}',
      leftActions: [
        // Actions de communication
        if (company.phoneNumber != null && company.phoneNumber!.isNotEmpty)
          SwipeAction.call(
            onPressed: () => _callCompany(company),
          ),
        if (company.email != null && company.email!.isNotEmpty)
          SwipeAction.email(
            onPressed: () => _emailCompany(company),
          ),
      ],
      rightActions: [
        // Actions de gestion
        SwipeAction.edit(
          onPressed: () => widget.onEditCompany(company),
        ),
        SwipeAction.delete(
          onPressed: () => widget.onDeleteCompany(company),
        ),
      ],
      child: cardContent,
    );
  }

  Widget _buildDesktopTable(List<Company> companies) {
    final theme = Theme.of(context);
    // Apply sorting
    companies.sort((a, b) {
      int result;
      switch (_sortColumnIndex) {
        case 1:
          result = (a.categories.isNotEmpty ? a.categories.first : '')
              .compareTo(b.categories.isNotEmpty ? b.categories.first : '');
          break;
        case 2:
          result = (a.email ?? '').compareTo(b.email ?? '');
          break;
        case 3:
          result = a.createdAt.compareTo(b.createdAt);
          break;
        case 0:
        default:
          result = a.name.compareTo(b.name);
      }
      return _sortAscending ? result : -result;
    });

    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: theme.colorScheme.outline.withAlpha(30),
                width: 1,
              ),
            ),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          child: Row(
            children: _buildTableHeaders(theme),
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: companies.length,
            padding: EdgeInsets.zero,
            itemBuilder: (context, index) {
              final company = companies[index];
              final isSelected = widget.selectedCompany?.id == company.id;
              return _buildCompanyRow(context, company, isSelected, theme);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCompanyRow(
      BuildContext context, Company company, bool isSelected, ThemeData theme) {
    return Material(
      color: isSelected
          ? theme.colorScheme.primary.withAlpha(15)
          : Colors.transparent,
      child: InkWell(
        onTap: () => widget.onCompanySelected(company),
        hoverColor: theme.colorScheme.primary.withAlpha(10),
        child: Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: theme.colorScheme.outline.withAlpha(15),
                width: 1,
              ),
            ),
          ),
          child: Padding(
            padding:
                const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Row(
                    children: [
                      _buildCompanyLogo(context, company),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              company.name,
                              style: TextStyle(
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                                color: isSelected
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.onSurface,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (company.siret != null)
                              Text(
                                'SIRET: ${company.siret}',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                _buildVerticalDivider(theme),
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: company.categories.isEmpty
                        ? Text(
                            'Non défini',
                            style: TextStyle(
                              color: theme.colorScheme.onSurface.withAlpha(150),
                              fontStyle: FontStyle.italic,
                            ),
                          )
                        : Wrap(
                            spacing: 4,
                            runSpacing: 4,
                            children: company.categories
                                .map((cat) => _buildCategoryChip(cat, theme))
                                .toList(),
                          ),
                  ),
                ),
                _buildVerticalDivider(theme),
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          company.email ?? 'N/A',
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          company.phoneNumber ?? 'N/A',
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
                _buildVerticalDivider(theme),
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Text(_dateFormat.format(company.createdAt)),
                  ),
                ),
                SizedBox(
                  width: 100,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      IconButton(
                        icon: Icon(Icons.edit_outlined,
                            color: theme.colorScheme.secondary),
                        onPressed: () => widget.onEditCompany(company),
                        tooltip: 'Modifier',
                        splashRadius: 20,
                      ),
                      IconButton(
                        icon: Icon(Icons.delete_outline,
                            color: theme.colorScheme.error),
                        onPressed: () => widget.onDeleteCompany(company),
                        tooltip: 'Supprimer',
                        splashRadius: 20,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCompanyLogo(BuildContext context, Company company,
      {double size = 36}) {
    final theme = Theme.of(context);
    final initials = _getInitials(company.name);

    // Couleur basée sur le hash du nom pour cohérence
    final colorIndex = (company.name.hashCode).abs() % 6;
    final colors = [
      theme.colorScheme.primary,
      Colors.blue.shade600,
      Colors.green.shade600,
      Colors.orange.shade600,
      Colors.purple.shade600,
      Colors.teal.shade600,
    ];
    final backgroundColor = colors[colorIndex];

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor.withValues(alpha: 0.1),
        shape: BoxShape.circle,
        border: Border.all(
          color: backgroundColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Center(
        child: Text(
          initials,
          style: TextStyle(
            color: backgroundColor,
            fontWeight: FontWeight.w700,
            fontSize: size * 0.4,
          ),
        ),
      ),
    );
  }

  List<Widget> _buildTableHeaders(ThemeData theme) {
    return [
      Expanded(
        flex: 3,
        child: _buildHeaderCell(theme, 'Nom', 0),
      ),
      _buildVerticalDivider(theme),
      Expanded(
        flex: 2,
        child: _buildHeaderCell(theme, 'Catégories', 1),
      ),
      _buildVerticalDivider(theme),
      Expanded(
        flex: 2,
        child: _buildHeaderCell(theme, 'Contact', 2),
      ),
      _buildVerticalDivider(theme),
      Expanded(
        flex: 2,
        child: _buildHeaderCell(theme, 'Créé le', 3),
      ),
      const SizedBox(width: 100),
    ];
  }

  Widget _buildVerticalDivider(ThemeData theme) {
    return Container(
      height: 30,
      width: 1,
      color: theme.colorScheme.outline.withAlpha(30),
    );
  }

  Widget _buildHeaderCell(ThemeData theme, String title, int columnIndex) {
    return InkWell(
      onTap: () => setState(() {
        if (_sortColumnIndex == columnIndex) {
          _sortAscending = !_sortAscending;
        } else {
          _sortColumnIndex = columnIndex;
          _sortAscending = true;
        }
      }),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: Row(
          children: [
            Text(
              title,
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            if (_sortColumnIndex == columnIndex)
              Icon(
                _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                size: 16,
                color: theme.colorScheme.primary,
              ),
          ],
        ),
      ),
    );
  }

  // Actions de swipe pour les entreprises
  void _callCompany(Company company) {
    if (company.phoneNumber != null && company.phoneNumber!.isNotEmpty) {
      HapticFeedback.mediumImpact();
      _launchUrl('tel:${company.phoneNumber}');
    }
  }

  void _emailCompany(Company company) {
    if (company.email != null && company.email!.isNotEmpty) {
      HapticFeedback.mediumImpact();
      _launchUrl('mailto:${company.email}');
    }
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      }
    } catch (e) {
      // Gérer l'erreur silencieusement ou afficher un message
      debugPrint('Erreur lors du lancement de l\'URL: $e');
    }
  }
}
