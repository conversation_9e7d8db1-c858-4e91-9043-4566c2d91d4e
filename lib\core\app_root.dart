import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logging/logging.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../theme/app_theme.dart';
import '../theme/theme_mode_provider.dart';
import '../theme/mobile_theme_extensions.dart';
import '../core/providers/auth_providers.dart';
import '../core/routing/app_router.dart';
import '../core/utils/platform_utils.dart';

class AppRoot extends ConsumerWidget {
  const AppRoot({super.key});

  /// Applique les optimisations mobiles au thème si nécessaire
  ThemeData _applyMobileOptimizations(BuildContext context, ThemeData theme) {
    if (PlatformUtils.shouldUseMobileOptimizations(context)) {
      return MobileThemeExtensions.applyMobileOptimizations(context, theme);
    }
    return theme;
  }

  Widget _buildUnauthenticatedApp(
      Logger logger, AppTheme appTheme, ThemeMode themeMode) {
    return Builder(
      builder: (context) {
        final lightTheme = _applyMobileOptimizations(context, appTheme.light());
        final darkTheme = _applyMobileOptimizations(context, appTheme.dark());

        return MaterialApp.router(
          title: 'Seqqo',
          theme: lightTheme,
          darkTheme: darkTheme,
          themeMode: themeMode,
          routerConfig: appRouter,
          debugShowCheckedModeBanner: false,
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('fr', 'FR'),
          ],
        );
      },
    );
  }

  Widget _buildAuthenticatedApp(
      Logger logger, AppTheme appTheme, ThemeMode themeMode, User user) {
    return Builder(
      builder: (context) {
        final lightTheme = _applyMobileOptimizations(context, appTheme.light());
        final darkTheme = _applyMobileOptimizations(context, appTheme.dark());

        return MaterialApp.router(
          title: 'Seqqo',
          theme: lightTheme,
          darkTheme: darkTheme,
          themeMode: themeMode,
          routerConfig: appRouter,
          debugShowCheckedModeBanner: false,
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('fr', 'FR'),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final logger = Logger('AppRoot');
    final appTheme = AppTheme.of(context);
    final themeMode = ref.watch(themeModeProvider);

    return Consumer(
      builder: (context, ref, child) {
        final authStateAsync = ref.watch(authStateProvider);

        return authStateAsync.when(
          data: (authState) {
            if (authState is AuthStateLoading) {
              logger.info("État d'authentification en cours de chargement");
              return MaterialApp(
                title: 'Seqqo',
                theme: appTheme.light(),
                darkTheme: appTheme.dark(),
                themeMode: themeMode,
                home: const Scaffold(
                  body: Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
                debugShowCheckedModeBanner: false,
              );
            } else if (authState is AuthStateAuthenticated) {
              final user = (authState).user;
              logger.info("Utilisateur authentifié: ${user.uid}");
              return _buildAuthenticatedApp(logger, appTheme, themeMode, user);
            } else {
              logger.info("Utilisateur non authentifié");
              return _buildUnauthenticatedApp(logger, appTheme, themeMode);
            }
          },
          loading: () {
            logger.info("État d'authentification en cours de chargement");
            return MaterialApp(
              title: 'Seqqo',
              theme: appTheme.light(),
              darkTheme: appTheme.dark(),
              themeMode: themeMode,
              home: const Scaffold(
                body: Center(
                  child: CircularProgressIndicator(),
                ),
              ),
              debugShowCheckedModeBanner: false,
            );
          },
          error: (error, stackTrace) {
            logger.severe("Erreur d'authentification", error, stackTrace);
            return MaterialApp(
              title: 'Seqqo - Erreur',
              theme: appTheme.light(),
              darkTheme: appTheme.dark(),
              themeMode: themeMode,
              home: Scaffold(
                body: Center(
                  child: Text('Erreur d\'authentification: $error'),
                ),
              ),
              debugShowCheckedModeBanner: false,
            );
          },
        );
      },
    );
  }
}
