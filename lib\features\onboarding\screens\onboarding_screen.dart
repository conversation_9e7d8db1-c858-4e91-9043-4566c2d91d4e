import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/services/onboarding_service.dart';
import '../../../core/utils/platform_utils.dart';
import '../../../core/utils/responsive_utils.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  final _onboardingService = OnboardingService();

  final List<OnboardingPage> _pages = [
    OnboardingPage(
      title: 'Bienvenue sur Seqqo',
      description:
          'La solution tout-en-un pour gérer vos chantiers de construction de manière efficace.',
      icon: Icons.architecture,
    ),
    OnboardingPage(
      title: 'Planifiez vos projets',
      description:
          '<PERSON><PERSON><PERSON> et suivez vos plannings de chantier avec des outils de planification avancés.',
      icon: Icons.calendar_today,
    ),
    OnboardingPage(
      title: '<PERSON><PERSON>rez vos équipes',
      description:
          'Organisez vos équipes et suivez l\'avancement des tâches en temps réel.',
      icon: Icons.group,
    ),
    OnboardingPage(
      title: 'Suivez vos chantiers',
      description:
          'Accédez à toutes les informations de vos chantiers où que vous soyez.',
      icon: Icons.construction,
    ),
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _completeOnboarding() async {
    // Ajouter un feedback haptique sur mobile
    if (PlatformUtils.isMobile) {
      PlatformUtils.triggerHapticFeedback(HapticFeedbackType.medium);
    }

    await _onboardingService.completeOnboarding();
    if (mounted) {
      context.go('/login');
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isLastPage = _currentPage == _pages.length - 1;

    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                itemCount: _pages.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemBuilder: (context, index) {
                  final page = _pages[index];
                  return Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          page.icon,
                          size: 120,
                          color: colorScheme.primary,
                        ),
                        const SizedBox(height: 32),
                        Text(
                          page.title,
                          style: Theme.of(context)
                              .textTheme
                              .headlineMedium
                              ?.copyWith(
                                color: colorScheme.onSurface,
                                fontWeight: FontWeight.bold,
                              ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          page.description,
                          style:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    color: colorScheme.onSurfaceVariant,
                                    height: 1.5,
                                  ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      _pages.length,
                      (index) => Container(
                        width: 8,
                        height: 8,
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: _currentPage == index
                              ? colorScheme.primary
                              : colorScheme.onSurfaceVariant.withValues(
                                  red: ((colorScheme.onSurfaceVariant.r * 255.0)
                                              .round() &
                                          0xff)
                                      .toDouble(),
                                  green:
                                      ((colorScheme.onSurfaceVariant.g * 255.0)
                                                  .round() &
                                              0xff)
                                          .toDouble(),
                                  blue:
                                      ((colorScheme.onSurfaceVariant.b * 255.0)
                                                  .round() &
                                              0xff)
                                          .toDouble(),
                                  alpha: 0.3,
                                ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 32),
                  SizedBox(
                    width: double.infinity,
                    child: FilledButton(
                      onPressed: isLastPage ? _completeOnboarding : _nextPage,
                      style: FilledButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: Text(
                        isLastPage ? 'Commencer' : 'Suivant',
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                  if (!isLastPage) ...[
                    const SizedBox(height: 12),
                    TextButton(
                      onPressed: _completeOnboarding,
                      child: const Text('Passer'),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class OnboardingPage {
  final String title;
  final String description;
  final IconData icon;

  const OnboardingPage({
    required this.title,
    required this.description,
    required this.icon,
  });
}
