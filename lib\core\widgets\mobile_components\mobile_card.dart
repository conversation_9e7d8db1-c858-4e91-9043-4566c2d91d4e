import 'package:flutter/material.dart';
import '../../utils/platform_utils.dart';
import '../../utils/responsive_utils.dart';

/// Carte optimisée pour les interactions mobiles
class MobileCard extends StatelessWidget {
  /// Contenu de la carte
  final Widget child;
  
  /// Callback pour les interactions tap
  final VoidCallback? onTap;
  
  /// Callback pour les interactions long press
  final VoidCallback? onLongPress;
  
  /// Marge de la carte
  final EdgeInsets? margin;
  
  /// Padding interne de la carte
  final EdgeInsets? padding;
  
  /// Couleur de fond
  final Color? backgroundColor;
  
  /// Élévation de la carte
  final double? elevation;
  
  /// Couleur de l'ombre
  final Color? shadowColor;
  
  /// Forme de la carte
  final ShapeBorder? shape;
  
  /// Si la carte doit avoir un effet de ripple
  final bool enableRipple;
  
  /// Couleur du ripple
  final Color? rippleColor;
  
  /// Si la carte doit avoir un feedback haptique
  final bool enableHapticFeedback;
  
  /// Type de feedback haptique
  final HapticFeedbackType hapticFeedbackType;

  const MobileCard({
    super.key,
    required this.child,
    this.onTap,
    this.onLongPress,
    this.margin,
    this.padding,
    this.backgroundColor,
    this.elevation,
    this.shadowColor,
    this.shape,
    this.enableRipple = true,
    this.rippleColor,
    this.enableHapticFeedback = true,
    this.hapticFeedbackType = HapticFeedbackType.light,
  });

  @override
  Widget build(BuildContext context) {
    final isMobile = PlatformUtils.shouldUseMobileOptimizations(context);
    
    // Ajuster les propriétés pour mobile
    final adaptiveMargin = margin ?? 
        (isMobile 
            ? const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0)
            : const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0));
    
    final adaptivePadding = padding ?? 
        (isMobile 
            ? const EdgeInsets.all(16.0)
            : const EdgeInsets.all(20.0));
    
    final adaptiveElevation = elevation ?? 
        (isMobile ? 2.0 : 4.0);
    
    final adaptiveShape = shape ?? 
        RoundedRectangleBorder(
          borderRadius: PlatformUtils.getBorderRadius(isLarge: !isMobile),
        );

    final card = Card(
      margin: adaptiveMargin,
      elevation: adaptiveElevation,
      shadowColor: shadowColor ?? Colors.black.withValues(alpha: 0.1),
      color: backgroundColor,
      shape: adaptiveShape,
      child: Padding(
        padding: adaptivePadding,
        child: child,
      ),
    );

    // Si aucune interaction n'est définie, retourner la carte simple
    if (onTap == null && onLongPress == null) {
      return card;
    }

    // Créer le widget interactif
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap != null ? () {
          if (enableHapticFeedback && PlatformUtils.isMobile) {
            PlatformUtils.triggerHapticFeedback(hapticFeedbackType);
          }
          onTap!();
        } : null,
        onLongPress: onLongPress != null ? () {
          if (enableHapticFeedback && PlatformUtils.isMobile) {
            PlatformUtils.triggerHapticFeedback(HapticFeedbackType.medium);
          }
          onLongPress!();
        } : null,
        borderRadius: adaptiveShape is RoundedRectangleBorder 
            ? adaptiveShape.borderRadius as BorderRadius?
            : PlatformUtils.getBorderRadius(isLarge: !isMobile),
        splashColor: enableRipple 
            ? (rippleColor ?? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1))
            : Colors.transparent,
        highlightColor: enableRipple 
            ? (rippleColor ?? Theme.of(context).colorScheme.primary.withValues(alpha: 0.05))
            : Colors.transparent,
        child: card,
      ),
    );
  }
}

/// Carte d'action optimisée pour mobile avec icône et texte
class MobileActionCard extends StatelessWidget {
  /// Icône de l'action
  final IconData icon;
  
  /// Titre de l'action
  final String title;
  
  /// Description optionnelle
  final String? subtitle;
  
  /// Callback pour l'action
  final VoidCallback onTap;
  
  /// Couleur de l'icône
  final Color? iconColor;
  
  /// Taille de l'icône
  final double? iconSize;
  
  /// Couleur de fond
  final Color? backgroundColor;
  
  /// Si la carte doit être en mode compact
  final bool isCompact;

  const MobileActionCard({
    super.key,
    required this.icon,
    required this.title,
    required this.onTap,
    this.subtitle,
    this.iconColor,
    this.iconSize,
    this.backgroundColor,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isMobile = PlatformUtils.shouldUseMobileOptimizations(context);
    
    final adaptiveIconSize = iconSize ?? 
        (isMobile ? (isCompact ? 20.0 : 24.0) : (isCompact ? 18.0 : 22.0));

    return MobileCard(
      onTap: onTap,
      backgroundColor: backgroundColor,
      padding: isCompact 
          ? const EdgeInsets.all(12.0)
          : const EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: adaptiveIconSize,
            color: iconColor ?? theme.colorScheme.primary,
          ),
          SizedBox(height: isCompact ? 8.0 : 12.0),
          Text(
            title,
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          if (subtitle != null) ...[
            SizedBox(height: isCompact ? 4.0 : 6.0),
            Text(
              subtitle!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }
}

/// Carte d'information optimisée pour mobile
class MobileInfoCard extends StatelessWidget {
  /// Titre de l'information
  final String title;
  
  /// Valeur à afficher
  final String value;
  
  /// Icône optionnelle
  final IconData? icon;
  
  /// Couleur de l'icône
  final Color? iconColor;
  
  /// Couleur de fond
  final Color? backgroundColor;
  
  /// Callback pour l'interaction
  final VoidCallback? onTap;
  
  /// Si la carte doit être en mode compact
  final bool isCompact;

  const MobileInfoCard({
    super.key,
    required this.title,
    required this.value,
    this.icon,
    this.iconColor,
    this.backgroundColor,
    this.onTap,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return MobileCard(
      onTap: onTap,
      backgroundColor: backgroundColor,
      padding: isCompact 
          ? const EdgeInsets.all(12.0)
          : const EdgeInsets.all(16.0),
      child: Row(
        children: [
          if (icon != null) ...[
            Icon(
              icon,
              size: isCompact ? 20.0 : 24.0,
              color: iconColor ?? theme.colorScheme.primary,
            ),
            SizedBox(width: isCompact ? 12.0 : 16.0),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: isCompact ? 2.0 : 4.0),
                Text(
                  value,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          if (onTap != null) ...[
            SizedBox(width: isCompact ? 8.0 : 12.0),
            Icon(
              Icons.chevron_right,
              size: isCompact ? 18.0 : 20.0,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ],
        ],
      ),
    );
  }
}
