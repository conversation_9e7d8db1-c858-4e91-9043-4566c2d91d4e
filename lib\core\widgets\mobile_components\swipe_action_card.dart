import 'package:flutter/material.dart';
import 'package:seqqo/core/utils/platform_utils.dart';
import 'package:seqqo/core/utils/responsive_utils.dart';

/// Carte avec actions de swipe et appui long pour mobile
class SwipeActionCard extends StatelessWidget {
  final Widget child;
  final List<SwipeAction> leftActions;
  final List<SwipeAction> rightActions;
  final double actionExtent;
  final bool enabled;
  final bool enableLongPress;
  final String? longPressTitle;

  const SwipeActionCard({
    super.key,
    required this.child,
    this.leftActions = const [],
    this.rightActions = const [],
    this.actionExtent = 80.0,
    this.enabled = true,
    this.enableLongPress = true,
    this.longPressTitle,
  });

  @override
  Widget build(BuildContext context) {
    if (!enabled || (!ResponsiveUtils.isMobile(context))) {
      return child;
    }

    Widget swipeWidget = Dismissible(
      key: Unique<PERSON>ey(),
      direction: _getDismissDirection(),
      confirmDismiss: (direction) async {
        // Ne pas supprimer automatiquement, juste déclencher l'action
        _handleSwipe(direction);
        return false;
      },
      background: _buildLeftBackground(context),
      secondaryBackground: _buildRightBackground(context),
      child: child,
    );

    // Ajouter la gestion des appuis longs si activée
    if (enableLongPress &&
        (leftActions.isNotEmpty || rightActions.isNotEmpty)) {
      return GestureDetector(
        onLongPress: () => _showActionMenu(context),
        child: swipeWidget,
      );
    }

    return swipeWidget;
  }

  DismissDirection _getDismissDirection() {
    if (leftActions.isNotEmpty && rightActions.isNotEmpty) {
      return DismissDirection.horizontal;
    } else if (leftActions.isNotEmpty) {
      return DismissDirection.startToEnd;
    } else if (rightActions.isNotEmpty) {
      return DismissDirection.endToStart;
    }
    return DismissDirection.none;
  }

  void _handleSwipe(DismissDirection direction) {
    if (direction == DismissDirection.startToEnd && leftActions.isNotEmpty) {
      leftActions.first.onPressed();
    } else if (direction == DismissDirection.endToStart &&
        rightActions.isNotEmpty) {
      rightActions.first.onPressed();
    }
  }

  void _showActionMenu(BuildContext context) {
    PlatformUtils.triggerHapticFeedback(HapticFeedbackType.medium);

    final allActions = [...leftActions, ...rightActions];
    if (allActions.isEmpty) return;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: Theme.of(context)
                      .colorScheme
                      .outline
                      .withValues(alpha: 0.4),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Title
              if (longPressTitle != null) ...[
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    longPressTitle!,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
                const Divider(height: 1),
              ],

              // Actions
              ...allActions.map((action) => ListTile(
                    leading: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: action.backgroundColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        action.icon,
                        color: action.backgroundColor,
                        size: 20,
                      ),
                    ),
                    title: Text(action.label),
                    onTap: () {
                      Navigator.of(context).pop();
                      action.onPressed();
                    },
                  )),

              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLeftBackground(BuildContext context) {
    if (leftActions.isEmpty) return Container();

    final action = leftActions.first;
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.only(left: 20),
      color: action.backgroundColor,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            action.icon,
            color: action.foregroundColor,
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            action.label,
            style: TextStyle(
              color: action.foregroundColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRightBackground(BuildContext context) {
    if (rightActions.isEmpty) return Container();

    final action = rightActions.first;
    return Container(
      alignment: Alignment.centerRight,
      padding: const EdgeInsets.only(right: 20),
      color: action.backgroundColor,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            action.icon,
            color: action.foregroundColor,
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            action.label,
            style: TextStyle(
              color: action.foregroundColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

/// Action de swipe
class SwipeAction {
  final IconData icon;
  final String label;
  final Color backgroundColor;
  final Color foregroundColor;
  final VoidCallback onPressed;

  const SwipeAction({
    required this.icon,
    required this.label,
    required this.backgroundColor,
    required this.foregroundColor,
    required this.onPressed,
  });

  // Actions prédéfinies
  static SwipeAction edit({
    required VoidCallback onPressed,
    Color? backgroundColor,
    Color? foregroundColor,
  }) {
    return SwipeAction(
      icon: Icons.edit_rounded,
      label: 'Modifier',
      backgroundColor: backgroundColor ?? Colors.blue.shade500,
      foregroundColor: foregroundColor ?? Colors.white,
      onPressed: onPressed,
    );
  }

  static SwipeAction delete({
    required VoidCallback onPressed,
    Color? backgroundColor,
    Color? foregroundColor,
  }) {
    return SwipeAction(
      icon: Icons.delete_rounded,
      label: 'Supprimer',
      backgroundColor: backgroundColor ?? Colors.red.shade500,
      foregroundColor: foregroundColor ?? Colors.white,
      onPressed: onPressed,
    );
  }

  static SwipeAction favorite({
    required VoidCallback onPressed,
    bool isFavorite = false,
    Color? backgroundColor,
    Color? foregroundColor,
  }) {
    return SwipeAction(
      icon: isFavorite ? Icons.favorite : Icons.favorite_border,
      label: isFavorite ? 'Retirer' : 'Favoris',
      backgroundColor: backgroundColor ?? Colors.orange.shade500,
      foregroundColor: foregroundColor ?? Colors.white,
      onPressed: onPressed,
    );
  }

  static SwipeAction call({
    required VoidCallback onPressed,
    Color? backgroundColor,
    Color? foregroundColor,
  }) {
    return SwipeAction(
      icon: Icons.phone_rounded,
      label: 'Appeler',
      backgroundColor: backgroundColor ?? Colors.green.shade500,
      foregroundColor: foregroundColor ?? Colors.white,
      onPressed: onPressed,
    );
  }

  static SwipeAction email({
    required VoidCallback onPressed,
    Color? backgroundColor,
    Color? foregroundColor,
  }) {
    return SwipeAction(
      icon: Icons.email_rounded,
      label: 'Email',
      backgroundColor: backgroundColor ?? Colors.purple.shade500,
      foregroundColor: foregroundColor ?? Colors.white,
      onPressed: onPressed,
    );
  }
}

/// Widget pour actions rapides flottantes
class QuickActionFab extends StatelessWidget {
  final List<QuickAction> actions;
  final bool isExpanded;
  final VoidCallback onToggle;

  const QuickActionFab({
    super.key,
    required this.actions,
    this.isExpanded = false,
    required this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Actions expandues
        if (isExpanded) ...[
          for (int i = actions.length - 1; i >= 0; i--) ...[
            _buildActionButton(context, actions[i], theme),
            const SizedBox(height: 12),
          ],
        ],

        // Bouton principal
        FloatingActionButton(
          onPressed: () {
            if (ResponsiveUtils.isMobile(context)) {
              PlatformUtils.triggerHapticFeedback(HapticFeedbackType.medium);
            }
            onToggle();
          },
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: Colors.white,
          child: AnimatedRotation(
            turns: isExpanded ? 0.125 : 0,
            duration: const Duration(milliseconds: 200),
            child: Icon(isExpanded ? Icons.close : Icons.add),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(
      BuildContext context, QuickAction action, ThemeData theme) {
    return FloatingActionButton.small(
      onPressed: () {
        if (ResponsiveUtils.isMobile(context)) {
          PlatformUtils.triggerHapticFeedback(HapticFeedbackType.light);
        }
        action.onPressed();
      },
      backgroundColor: action.backgroundColor ?? theme.colorScheme.secondary,
      foregroundColor: action.foregroundColor ?? Colors.white,
      heroTag: action.label,
      tooltip: action.label,
      child: Icon(action.icon, size: 20),
    );
  }
}

/// Action rapide pour le FAB
class QuickAction {
  final IconData icon;
  final String label;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final VoidCallback onPressed;

  const QuickAction({
    required this.icon,
    required this.label,
    this.backgroundColor,
    this.foregroundColor,
    required this.onPressed,
  });
}
