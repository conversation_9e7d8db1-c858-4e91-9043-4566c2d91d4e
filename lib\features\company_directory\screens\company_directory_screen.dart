import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seqqo/core/widgets/slot_layout.dart';
import 'package:logging/logging.dart';

import 'package:seqqo/core/widgets/segmented_button.dart';
import 'package:seqqo/core/widgets/mobile_components/mobile_fab.dart';
import 'package:seqqo/core/widgets/mobile_components/mobile_card.dart';
import 'package:seqqo/core/widgets/mobile_components/mobile_toolbar.dart';
import 'package:seqqo/core/widgets/mobile_components/mobile_search_interface.dart';
import 'package:seqqo/core/utils/platform_utils.dart';
import 'package:seqqo/core/utils/responsive_utils.dart';
import 'package:seqqo/features/company_directory/models/company_model.dart';
import 'package:seqqo/features/company_directory/screens/company_detail_screen.dart';
import 'package:seqqo/features/company_directory/screens/company_form_screen.dart';
import 'package:seqqo/features/company_directory/services/company_repository.dart';
import 'package:seqqo/features/company_directory/utils/category_utils.dart';
import 'package:seqqo/features/company_directory/widgets/company_list_widget.dart';

// Provider to stream the list of companies
final companiesStreamProvider =
    StreamProvider.autoDispose<List<Company>>((ref) {
  return ref.watch(companyRepositoryProvider).getCompaniesStream();
});

class CompanyDirectoryScreen extends ConsumerStatefulWidget {
  const CompanyDirectoryScreen({super.key});

  @override
  ConsumerState<CompanyDirectoryScreen> createState() =>
      _CompanyDirectoryScreenState();
}

class _CompanyDirectoryScreenState
    extends ConsumerState<CompanyDirectoryScreen> {
  final Logger _logger = Logger('CompanyDirectoryScreen');
  Company? _selectedCompany;
  String _filterCategory = 'Toutes';
  String _sortBy = 'Nom';
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      if (mounted) {
        setState(() {
          _searchQuery = _searchController.text;
        });
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ResponsiveUtils.isMobile(context);

    final secondaryBody = _selectedCompany != null
        ? SlotLayout(
            config: <ResponsiveBreakpoint, ResponsiveLayoutConfig>{
              ResponsiveBreakpoint.medium: ResponsiveLayoutConfig(
                builder: (_) =>
                    CompanyDetailScreen(companyId: _selectedCompany!.id),
              ),
              ResponsiveBreakpoint.large: ResponsiveLayoutConfig(
                builder: (_) =>
                    CompanyDetailScreen(companyId: _selectedCompany!.id),
              ),
            },
          )
        : null;

    return Scaffold(
      body: Row(
        children: [
          Expanded(
            flex: isSmallScreen ? 1 : 2,
            child: SlotLayout(
              config: <ResponsiveBreakpoint, ResponsiveLayoutConfig>{
                ResponsiveBreakpoint.small: ResponsiveLayoutConfig(
                  builder: (_) => _buildMasterPane(context, true),
                ),
                ResponsiveBreakpoint.medium: ResponsiveLayoutConfig(
                  builder: (_) => _buildMasterPane(context, false),
                ),
                ResponsiveBreakpoint.large: ResponsiveLayoutConfig(
                  builder: (_) => _buildMasterPane(context, false),
                ),
              },
            ),
          ),
          if (!isSmallScreen && secondaryBody != null)
            Expanded(
              flex: 3,
              child: secondaryBody,
            ),
        ],
      ),
      floatingActionButton: isSmallScreen
          ? MobileExtendedFloatingActionButton(
              onPressed: () {
                if (ResponsiveUtils.isMobile(context)) {
                  PlatformUtils.triggerHapticFeedback(HapticFeedbackType.light);
                }
                _showCompanyFormDialog();
              },
              icon: const Icon(Icons.add),
              label: const Text('Ajouter'),
              tooltip: 'Ajouter une entreprise',
            )
          : null,
    );
  }

  Widget _buildMasterPane(BuildContext context, bool isSmallScreen) {
    final companiesAsync = ref.watch(companiesStreamProvider);
    return companiesAsync.when(
      data: (companies) => Column(
        children: [
          _buildHeader(context, companies),
          Expanded(
            child: CompanyListWidget(
              companies: _filterAndSortCompanies(companies),
              selectedCompany: _selectedCompany,
              isLoading: false,
              onCompanySelected: (company) {
                setState(() {
                  _selectedCompany = company;
                });
              },
              onDeleteCompany: _confirmDeleteCompany,
              onEditCompany: (company) {
                _showCompanyFormDialog(initialCompany: company);
              },
            ),
          ),
        ],
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (err, stack) => Center(child: Text('Erreur: ${err.toString()}')),
    );
  }

  Widget _buildHeader(BuildContext context, List<Company> companies) {
    final theme = Theme.of(context);
    final isSmallScreen = ResponsiveUtils.isMobile(context);

    final categoryCounts = <String, int>{};
    for (final company in companies) {
      for (final category in company.categories) {
        categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
      }
    }

    final categorySegments = [
      const ButtonSegment<String>(value: 'Toutes', label: Text('Toutes')),
      ...categoryCounts.entries.toList().take(5).map((e) =>
          ButtonSegment<String>(
              value: e.key,
              label: Text(e.key),
              icon: Icon(getCategoryIcon(e.key), size: 16)))
    ];

    final sortSegments = [
      const ButtonSegment<String>(
          value: 'Nom',
          label: Text('Nom'),
          icon: Icon(Icons.sort_by_alpha, size: 16)),
      const ButtonSegment<String>(
          value: 'Date',
          label: Text('Date'),
          icon: Icon(Icons.calendar_today_outlined, size: 16)),
      const ButtonSegment<String>(
          value: 'Catégorie',
          label: Text('Catégorie'),
          icon: Icon(Icons.category, size: 16)),
    ];

    return Padding(
      padding: EdgeInsets.all(ResponsiveUtils.getHorizontalPadding(context)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (isSmallScreen)
            Text('Annuaire', style: theme.textTheme.headlineSmall)
          else
            Text('Annuaire des entreprises',
                style: theme.textTheme.headlineMedium),
          SizedBox(height: ResponsiveUtils.getSpacing(context)),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: isSmallScreen
                        ? 'Rechercher...'
                        : 'Rechercher une entreprise, un contact...',
                    prefixIcon: Icon(
                      Icons.search,
                      size: ResponsiveUtils.getIconSize(context),
                    ),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: Icon(
                              Icons.clear,
                              size: ResponsiveUtils.getIconSize(context,
                                  isSmall: true),
                            ),
                            onPressed: () {
                              setState(() {
                                _searchController.clear();
                                _searchQuery = '';
                              });
                            },
                            tooltip: 'Effacer la recherche',
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: PlatformUtils.getBorderRadius(),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal:
                          ResponsiveUtils.getHorizontalPadding(context) * 0.75,
                      vertical:
                          ResponsiveUtils.getVerticalPadding(context) * 0.5,
                    ),
                  ),
                ),
              ),
              if (!isSmallScreen) ...[
                SizedBox(width: ResponsiveUtils.getSpacing(context)),
                FilledButton.icon(
                  onPressed: () => _showCompanyFormDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('Ajouter une entreprise'),
                ),
              ]
            ],
          ),
          SizedBox(height: ResponsiveUtils.getSpacing(context)),
          Wrap(
            spacing: ResponsiveUtils.getSpacing(context),
            runSpacing: ResponsiveUtils.getSpacing(context, isLarge: false),
            children: [
              AppSegmentedButton<String>(
                segments: categorySegments,
                selected: {_filterCategory},
                isCompact: isSmallScreen,
                onSelectionChanged: (newSelection) {
                  if (ResponsiveUtils.isMobile(context)) {
                    PlatformUtils.triggerHapticFeedback(
                        HapticFeedbackType.light);
                  }
                  setState(() => _filterCategory = newSelection.first);
                },
              ),
              AppSegmentedButton<String>(
                segments: sortSegments,
                selected: {_sortBy},
                isCompact: isSmallScreen,
                onSelectionChanged: (newSelection) {
                  if (ResponsiveUtils.isMobile(context)) {
                    PlatformUtils.triggerHapticFeedback(
                        HapticFeedbackType.light);
                  }
                  setState(() => _sortBy = newSelection.first);
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  List<Company> _filterAndSortCompanies(List<Company> companies) {
    List<Company> filteredCompanies = companies.where((company) {
      bool matchesCategory = _filterCategory == 'Toutes' ||
          company.categories.contains(_filterCategory);
      bool matchesSearch = _searchQuery.isEmpty ||
          company.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (company.address
                  ?.toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ??
              false) ||
          (company.phoneNumber
                  ?.toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ??
              false) ||
          (company.email?.toLowerCase().contains(_searchQuery.toLowerCase()) ??
              false) ||
          company.categories.any(
              (cat) => cat.toLowerCase().contains(_searchQuery.toLowerCase()));
      return matchesCategory && matchesSearch;
    }).toList();

    switch (_sortBy) {
      case 'Nom':
        filteredCompanies.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'Date':
        filteredCompanies.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'Catégorie':
        filteredCompanies.sort((a, b) {
          if (a.categories.isEmpty) return 1;
          if (b.categories.isEmpty) return -1;
          return a.categories.first.compareTo(b.categories.first);
        });
        break;
    }
    return filteredCompanies;
  }

  Future<void> _confirmDeleteCompany(Company company) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: Text(
            'Êtes-vous sûr de vouloir supprimer l\'entreprise "${company.name}" ? Cette action est irréversible.'),
        actions: [
          TextButton(
            child: const Text('Annuler'),
            onPressed: () => Navigator.of(context).pop(false),
          ),
          TextButton(
            style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.error),
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(companyRepositoryProvider).deleteCompany(company.id);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('Entreprise supprimée.'),
                backgroundColor: Colors.green),
          );
        }
        if (_selectedCompany?.id == company.id) {
          setState(() {
            _selectedCompany = null;
          });
        }
      } catch (e) {
        _logger.severe('Erreur de suppression: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
          );
        }
      }
    }
  }

  Future<bool?> _showCompanyFormDialog(
      {Company? initialCompany, int initialTab = 0}) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    final isSmallScreen = ResponsiveUtils.isMobile(context);

    return showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel:
          initialCompany != null ? 'Détails entreprise' : 'Nouvelle entreprise',
      barrierColor: Colors.black54,
      transitionDuration: PlatformUtils.getAnimationDuration(),
      pageBuilder: (context, animation, secondaryAnimation) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: PlatformUtils.getBorderRadius(isLarge: true),
          ),
          elevation: PlatformUtils.getElevation(isHigh: true),
          backgroundColor: theme.colorScheme.surface,
          insetPadding: EdgeInsets.symmetric(
            horizontal: isSmallScreen
                ? ResponsiveUtils.getHorizontalPadding(context)
                : size.width * 0.1,
            vertical: isSmallScreen
                ? ResponsiveUtils.getVerticalPadding(context)
                : size.height * 0.08,
          ),
          child: Container(
            width: isSmallScreen ? double.infinity : 800,
            constraints: BoxConstraints(
              maxWidth: 800,
              maxHeight: isSmallScreen ? size.height * 0.9 : 800,
            ),
            child: DefaultTabController(
              length: 2,
              initialIndex: initialTab,
              child: Scaffold(
                backgroundColor: Colors.transparent,
                appBar: AppBar(
                  elevation: 0,
                  backgroundColor: theme.colorScheme.surfaceContainerHighest,
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                  ),
                  title: Text(
                    initialCompany != null
                        ? 'Entreprise: ${initialCompany.name}'
                        : 'Nouvelle entreprise',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  leading: IconButton(
                    icon: const Icon(Icons.close, size: 20),
                    onPressed: () => Navigator.of(context).pop(),
                    tooltip: 'Fermer',
                  ),
                  bottom: const TabBar(
                    tabs: [
                      Tab(
                          text: 'Informations',
                          icon: Icon(Icons.edit_outlined, size: 18)),
                      Tab(
                          text: 'Détails',
                          icon: Icon(Icons.info_outlined, size: 18)),
                    ],
                    labelStyle:
                        TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                    indicatorSize: TabBarIndicatorSize.tab,
                    dividerColor: Colors.transparent,
                  ),
                ),
                body: Container(
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(12),
                      bottomRight: Radius.circular(12),
                    ),
                    color: theme.colorScheme.surface,
                  ),
                  child: TabBarView(
                    children: [
                      // Onglet Formulaire
                      CompanyFormScreen(initialCompany: initialCompany),

                      // Onglet Détails (seulement si on est en mode édition)
                      initialCompany != null
                          ? CompanyDetailScreen(companyId: initialCompany.id)
                          : Center(
                              child: Padding(
                                padding: const EdgeInsets.all(24.0),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.business_outlined,
                                      size: 64,
                                      color: theme.colorScheme.outline,
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      'Veuillez d\'abord créer l\'entreprise',
                                      style:
                                          theme.textTheme.titleMedium?.copyWith(
                                        color:
                                            theme.colorScheme.onSurfaceVariant,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'Les détails seront disponibles après la création de l\'entreprise.',
                                      textAlign: TextAlign.center,
                                      style:
                                          theme.textTheme.bodyMedium?.copyWith(
                                        color: theme
                                            .colorScheme.onSurfaceVariant
                                            .withAlpha(178),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: CurvedAnimation(
              parent: animation,
              curve: PlatformUtils.getAnimationCurve(),
            ),
            child: child,
          ),
        );
      },
    );
  }
}
