import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:seqqo/core/widgets/slot_layout.dart';
import 'package:logging/logging.dart';

import 'package:seqqo/core/widgets/segmented_button.dart';
import 'package:seqqo/core/widgets/mobile_components/mobile_fab.dart';
import 'package:seqqo/core/widgets/mobile_components/mobile_toolbar.dart';
import 'package:seqqo/core/widgets/mobile_components/mobile_search_interface.dart';
import 'package:seqqo/core/utils/platform_utils.dart';
import 'package:seqqo/core/utils/responsive_utils.dart';
import 'package:seqqo/features/company_directory/models/company_model.dart';
import 'package:seqqo/features/company_directory/screens/company_detail_screen.dart';
import 'package:seqqo/features/company_directory/screens/company_form_screen.dart';
import 'package:seqqo/features/company_directory/services/company_repository.dart';
import 'package:seqqo/features/company_directory/services/business_card_ocr_service.dart';
import 'package:seqqo/features/company_directory/utils/category_utils.dart';
import 'package:seqqo/features/company_directory/widgets/company_list_widget.dart';

// Provider to stream the list of companies
final companiesStreamProvider =
    StreamProvider.autoDispose<List<Company>>((ref) {
  return ref.watch(companyRepositoryProvider).getCompaniesStream();
});

class CompanyDirectoryScreen extends ConsumerStatefulWidget {
  const CompanyDirectoryScreen({super.key});

  @override
  ConsumerState<CompanyDirectoryScreen> createState() =>
      _CompanyDirectoryScreenState();
}

class _CompanyDirectoryScreenState
    extends ConsumerState<CompanyDirectoryScreen> {
  final Logger _logger = Logger('CompanyDirectoryScreen');
  Company? _selectedCompany;
  String _filterCategory = 'Toutes';
  String _sortBy = 'Nom';
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      if (mounted) {
        setState(() {
          _searchQuery = _searchController.text;
        });
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ResponsiveUtils.isMobile(context);

    final secondaryBody = _selectedCompany != null
        ? SlotLayout(
            config: <ResponsiveBreakpoint, ResponsiveLayoutConfig>{
              ResponsiveBreakpoint.medium: ResponsiveLayoutConfig(
                builder: (_) =>
                    CompanyDetailScreen(companyId: _selectedCompany!.id),
              ),
              ResponsiveBreakpoint.large: ResponsiveLayoutConfig(
                builder: (_) =>
                    CompanyDetailScreen(companyId: _selectedCompany!.id),
              ),
            },
          )
        : null;

    return Scaffold(
      body: Row(
        children: [
          Expanded(
            flex: isSmallScreen ? 1 : 2,
            child: SlotLayout(
              config: <ResponsiveBreakpoint, ResponsiveLayoutConfig>{
                ResponsiveBreakpoint.small: ResponsiveLayoutConfig(
                  builder: (_) => _buildMasterPane(context, true),
                ),
                ResponsiveBreakpoint.medium: ResponsiveLayoutConfig(
                  builder: (_) => _buildMasterPane(context, false),
                ),
                ResponsiveBreakpoint.large: ResponsiveLayoutConfig(
                  builder: (_) => _buildMasterPane(context, false),
                ),
              },
            ),
          ),
          if (!isSmallScreen && secondaryBody != null)
            Expanded(
              flex: 3,
              child: secondaryBody,
            ),
        ],
      ),
      floatingActionButton: isSmallScreen ? _buildMobileFAB() : null,
    );
  }

  Widget _buildMasterPane(BuildContext context, bool isSmallScreen) {
    final companiesAsync = ref.watch(companiesStreamProvider);
    return companiesAsync.when(
      data: (companies) => Column(
        children: [
          _buildHeader(context, companies),
          Expanded(
            child: CompanyListWidget(
              companies: _filterAndSortCompanies(companies),
              selectedCompany: _selectedCompany,
              isLoading: false,
              onCompanySelected: (company) {
                setState(() {
                  _selectedCompany = company;
                });
              },
              onDeleteCompany: _confirmDeleteCompany,
              onEditCompany: (company) {
                _showCompanyFormDialog(initialCompany: company);
              },
            ),
          ),
        ],
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (err, stack) => Center(child: Text('Erreur: ${err.toString()}')),
    );
  }

  Widget _buildHeader(BuildContext context, List<Company> companies) {
    final theme = Theme.of(context);
    final isMobile = ResponsiveUtils.isMobile(context);

    if (isMobile) {
      // Toolbar mobile moderne (style Projets)
      return Column(
        children: [
          MobileToolbar(
            title: 'Annuaire',
            onSearchTap: _openMobileSearch,
            onRefresh: _loadCompanies,
            isLoading: _isLoading,
            showFilterBadge: true,
            activeFilterCount: _getActiveFilterCount(),
            filterWidgets: [
              // Filtre de catégorie
              MobileFilterChip(
                label: _filterCategory,
                icon: _getCategoryIcon(_filterCategory),
                isSelected: _filterCategory != 'Toutes',
                selectedColor: _getCategoryColor(_filterCategory),
                onTap: () => _showCategoryFilterDialog(companies),
              ),
              // Filtre de tri
              MobileFilterChip(
                label: _sortBy,
                icon: _getSortIcon(_sortBy),
                isSelected: _sortBy != 'Nom',
                onTap: () => _showSortFilterDialog(),
              ),
            ],
          ),

          // Statistiques compactes intégrées (style Projets)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: _buildStatsRow(companies, true),
          ),
        ],
      );
    }

    // Version desktop (conservée)
    final categoryCounts = <String, int>{};
    for (final company in companies) {
      for (final category in company.categories) {
        categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
      }
    }

    final categorySegments = [
      const ButtonSegment<String>(value: 'Toutes', label: Text('Toutes')),
      ...categoryCounts.entries.toList().take(5).map((e) =>
          ButtonSegment<String>(
              value: e.key,
              label: Text(e.key),
              icon: Icon(getCategoryIcon(e.key), size: 16)))
    ];

    final sortSegments = [
      const ButtonSegment<String>(
          value: 'Nom',
          label: Text('Nom'),
          icon: Icon(Icons.sort_by_alpha, size: 16)),
      const ButtonSegment<String>(
          value: 'Date',
          label: Text('Date'),
          icon: Icon(Icons.calendar_today_outlined, size: 16)),
      const ButtonSegment<String>(
          value: 'Catégorie',
          label: Text('Catégorie'),
          icon: Icon(Icons.category, size: 16)),
    ];

    return Padding(
      padding: EdgeInsets.all(ResponsiveUtils.getHorizontalPadding(context)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Annuaire des entreprises',
              style: theme.textTheme.headlineMedium),
          SizedBox(height: ResponsiveUtils.getSpacing(context)),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Rechercher une entreprise, un contact...',
                    prefixIcon: Icon(
                      Icons.search,
                      size: ResponsiveUtils.getIconSize(context),
                    ),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: Icon(
                              Icons.clear,
                              size: ResponsiveUtils.getIconSize(context,
                                  isSmall: true),
                            ),
                            onPressed: () {
                              setState(() {
                                _searchController.clear();
                                _searchQuery = '';
                              });
                            },
                            tooltip: 'Effacer la recherche',
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: PlatformUtils.getBorderRadius(),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    isDense: true,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal:
                          ResponsiveUtils.getHorizontalPadding(context) * 0.75,
                      vertical:
                          ResponsiveUtils.getVerticalPadding(context) * 0.5,
                    ),
                  ),
                ),
              ),
              SizedBox(width: ResponsiveUtils.getSpacing(context)),
              FilledButton.icon(
                onPressed: () => _showCompanyFormDialog(),
                icon: const Icon(Icons.add),
                label: const Text('Ajouter une entreprise'),
              ),
            ],
          ),
          SizedBox(height: ResponsiveUtils.getSpacing(context)),
          Wrap(
            spacing: ResponsiveUtils.getSpacing(context),
            runSpacing: ResponsiveUtils.getSpacing(context, isLarge: false),
            children: [
              AppSegmentedButton<String>(
                segments: categorySegments,
                selected: {_filterCategory},
                isCompact: false,
                onSelectionChanged: (newSelection) {
                  setState(() => _filterCategory = newSelection.first);
                },
              ),
              AppSegmentedButton<String>(
                segments: sortSegments,
                selected: {_sortBy},
                isCompact: false,
                onSelectionChanged: (newSelection) {
                  setState(() => _sortBy = newSelection.first);
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  List<Company> _filterAndSortCompanies(List<Company> companies) {
    List<Company> filteredCompanies = companies.where((company) {
      bool matchesCategory = _filterCategory == 'Toutes' ||
          company.categories.contains(_filterCategory);
      bool matchesSearch = _searchQuery.isEmpty ||
          company.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          (company.address
                  ?.toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ??
              false) ||
          (company.phoneNumber
                  ?.toLowerCase()
                  .contains(_searchQuery.toLowerCase()) ??
              false) ||
          (company.email?.toLowerCase().contains(_searchQuery.toLowerCase()) ??
              false) ||
          company.categories.any(
              (cat) => cat.toLowerCase().contains(_searchQuery.toLowerCase()));
      return matchesCategory && matchesSearch;
    }).toList();

    switch (_sortBy) {
      case 'Nom':
        filteredCompanies.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'Date':
        filteredCompanies.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'Catégorie':
        filteredCompanies.sort((a, b) {
          if (a.categories.isEmpty) return 1;
          if (b.categories.isEmpty) return -1;
          return a.categories.first.compareTo(b.categories.first);
        });
        break;
    }
    return filteredCompanies;
  }

  Future<void> _confirmDeleteCompany(Company company) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: Text(
            'Êtes-vous sûr de vouloir supprimer l\'entreprise "${company.name}" ? Cette action est irréversible.'),
        actions: [
          TextButton(
            child: const Text('Annuler'),
            onPressed: () => Navigator.of(context).pop(false),
          ),
          TextButton(
            style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.error),
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(companyRepositoryProvider).deleteCompany(company.id);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('Entreprise supprimée.'),
                backgroundColor: Colors.green),
          );
        }
        if (_selectedCompany?.id == company.id) {
          setState(() {
            _selectedCompany = null;
          });
        }
      } catch (e) {
        _logger.severe('Erreur de suppression: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
          );
        }
      }
    }
  }

  Future<bool?> _showCompanyFormDialog(
      {Company? initialCompany, int initialTab = 0}) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;
    final isSmallScreen = ResponsiveUtils.isMobile(context);

    return showGeneralDialog(
      context: context,
      barrierDismissible: true,
      barrierLabel:
          initialCompany != null ? 'Détails entreprise' : 'Nouvelle entreprise',
      barrierColor: Colors.black54,
      transitionDuration: PlatformUtils.getAnimationDuration(),
      pageBuilder: (context, animation, secondaryAnimation) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: PlatformUtils.getBorderRadius(isLarge: true),
          ),
          elevation: PlatformUtils.getElevation(isHigh: true),
          backgroundColor: theme.colorScheme.surface,
          insetPadding: EdgeInsets.symmetric(
            horizontal: isSmallScreen
                ? ResponsiveUtils.getHorizontalPadding(context)
                : size.width * 0.1,
            vertical: isSmallScreen
                ? ResponsiveUtils.getVerticalPadding(context)
                : size.height * 0.08,
          ),
          child: Container(
            width: isSmallScreen ? double.infinity : 800,
            constraints: BoxConstraints(
              maxWidth: 800,
              maxHeight: isSmallScreen ? size.height * 0.9 : 800,
            ),
            child: DefaultTabController(
              length: 2,
              initialIndex: initialTab,
              child: Scaffold(
                backgroundColor: Colors.transparent,
                appBar: AppBar(
                  elevation: 0,
                  backgroundColor: theme.colorScheme.surfaceContainerHighest,
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                  ),
                  title: Text(
                    initialCompany != null
                        ? 'Entreprise: ${initialCompany.name}'
                        : 'Nouvelle entreprise',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  leading: IconButton(
                    icon: const Icon(Icons.close, size: 20),
                    onPressed: () => Navigator.of(context).pop(),
                    tooltip: 'Fermer',
                  ),
                  bottom: const TabBar(
                    tabs: [
                      Tab(
                          text: 'Informations',
                          icon: Icon(Icons.edit_outlined, size: 18)),
                      Tab(
                          text: 'Détails',
                          icon: Icon(Icons.info_outlined, size: 18)),
                    ],
                    labelStyle:
                        TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                    indicatorSize: TabBarIndicatorSize.tab,
                    dividerColor: Colors.transparent,
                  ),
                ),
                body: Container(
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(12),
                      bottomRight: Radius.circular(12),
                    ),
                    color: theme.colorScheme.surface,
                  ),
                  child: TabBarView(
                    children: [
                      // Onglet Formulaire
                      CompanyFormScreen(initialCompany: initialCompany),

                      // Onglet Détails (seulement si on est en mode édition)
                      initialCompany != null
                          ? CompanyDetailScreen(companyId: initialCompany.id)
                          : Center(
                              child: Padding(
                                padding: const EdgeInsets.all(24.0),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.business_outlined,
                                      size: 64,
                                      color: theme.colorScheme.outline,
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      'Veuillez d\'abord créer l\'entreprise',
                                      style:
                                          theme.textTheme.titleMedium?.copyWith(
                                        color:
                                            theme.colorScheme.onSurfaceVariant,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'Les détails seront disponibles après la création de l\'entreprise.',
                                      textAlign: TextAlign.center,
                                      style:
                                          theme.textTheme.bodyMedium?.copyWith(
                                        color: theme
                                            .colorScheme.onSurfaceVariant
                                            .withAlpha(178),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: CurvedAnimation(
              parent: animation,
              curve: PlatformUtils.getAnimationCurve(),
            ),
            child: child,
          ),
        );
      },
    );
  }

  // Méthodes pour la toolbar mobile moderne
  void _openMobileSearch() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => MobileSearchInterface(
        hintText: 'Rechercher une entreprise...',
        onQueryChanged: (query) {
          setState(() {
            _searchQuery = query;
            _searchController.text = query;
          });
        },
        onClear: () {
          setState(() {
            _searchQuery = '';
            _searchController.clear();
          });
        },
        initialQuery: _searchQuery,
        onSuggestionTap: (suggestion) {
          setState(() {
            _searchQuery = suggestion;
            _searchController.text = suggestion;
          });
          Navigator.pop(context);
        },
      ),
    );
  }

  Future<void> _loadCompanies() async {
    setState(() {
      _isLoading = true;
    });

    // Simuler un délai de chargement
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() {
      _isLoading = false;
    });
  }

  int _getActiveFilterCount() {
    int count = 0;
    if (_filterCategory != 'Toutes') count++;
    if (_sortBy != 'Nom') count++;
    if (_searchQuery.isNotEmpty) count++;
    return count;
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Toutes':
        return Icons.business;
      case 'Restaurant':
        return Icons.restaurant;
      case 'Commerce':
        return Icons.store;
      case 'Service':
        return Icons.build;
      case 'Industrie':
        return Icons.factory;
      default:
        return Icons.category;
    }
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case 'Restaurant':
        return Colors.orange.shade600;
      case 'Commerce':
        return Colors.blue.shade600;
      case 'Service':
        return Colors.green.shade600;
      case 'Industrie':
        return Colors.purple.shade600;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }

  IconData _getSortIcon(String sortBy) {
    switch (sortBy) {
      case 'Nom':
        return Icons.sort_by_alpha;
      case 'Date':
        return Icons.calendar_today_outlined;
      case 'Catégorie':
        return Icons.category;
      default:
        return Icons.sort;
    }
  }

  void _showCategoryFilterDialog(List<Company> companies) {
    final categoryCounts = <String, int>{};
    for (final company in companies) {
      for (final category in company.categories) {
        categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
      }
    }

    final categories = ['Toutes', ...categoryCounts.keys];

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.outline.withAlpha(100),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text(
                  'Filtrer par catégorie',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),

              const Divider(height: 1),

              ...categories.map((category) => ListTile(
                    leading: Icon(_getCategoryIcon(category)),
                    title: Text(category),
                    trailing: category != 'Toutes'
                        ? Text('${categoryCounts[category]}')
                        : Text('${companies.length}'),
                    selected: _filterCategory == category,
                    onTap: () {
                      setState(() {
                        _filterCategory = category;
                      });
                      Navigator.pop(context);
                    },
                  )),

              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  void _showSortFilterDialog() {
    final sortOptions = ['Nom', 'Date', 'Catégorie'];

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.outline.withAlpha(100),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Text(
                  'Trier par',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),

              const Divider(height: 1),

              ...sortOptions.map((option) => ListTile(
                    leading: Icon(_getSortIcon(option)),
                    title: Text(option),
                    selected: _sortBy == option,
                    onTap: () {
                      setState(() {
                        _sortBy = option;
                      });
                      Navigator.pop(context);
                    },
                  )),

              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatsRow(List<Company> companies, bool isMobile) {
    final theme = Theme.of(context);
    final totalCompanies = companies.length;
    final filteredCompanies = _filterAndSortCompanies(companies);

    final categoryCounts = <String, int>{};
    for (final company in companies) {
      for (final category in company.categories) {
        categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
      }
    }

    final topCategories = categoryCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildStatChip(
            'Total',
            totalCompanies.toString(),
            Icons.business,
            theme.colorScheme.primary,
            isMobile,
          ),
          const SizedBox(width: 8),
          _buildStatChip(
            'Affichées',
            filteredCompanies.length.toString(),
            Icons.filter_list,
            Colors.blue.shade600,
            isMobile,
          ),
          if (topCategories.isNotEmpty) ...[
            const SizedBox(width: 8),
            _buildStatChip(
              topCategories.first.key,
              topCategories.first.value.toString(),
              _getCategoryIcon(topCategories.first.key),
              _getCategoryColor(topCategories.first.key),
              isMobile,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatChip(
      String label, String count, IconData icon, Color color, bool isMobile) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isMobile ? 8 : 12,
        vertical: isMobile ? 6 : 8,
      ),
      decoration: BoxDecoration(
        color: color.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withAlpha(60), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: isMobile ? 14 : 16,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            count,
            style: TextStyle(
              fontSize: isMobile ? 12 : 14,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
          const SizedBox(width: 2),
          Text(
            label,
            style: TextStyle(
              fontSize: isMobile ? 11 : 12,
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurface.withAlpha(150),
            ),
          ),
        ],
      ),
    );
  }

  // Bouton flottant mobile avec options
  Widget _buildMobileFAB() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Bouton scanner carte de visite
        FloatingActionButton(
          heroTag: "scan_card",
          onPressed: () {
            if (ResponsiveUtils.isMobile(context)) {
              PlatformUtils.triggerHapticFeedback(HapticFeedbackType.medium);
            }
            _scanBusinessCard();
          },
          backgroundColor: Theme.of(context).colorScheme.secondary,
          tooltip: 'Scanner une carte de visite',
          child: const Icon(Icons.document_scanner),
        ),
        const SizedBox(height: 16),

        // Bouton ajouter manuellement
        FloatingActionButton.extended(
          heroTag: "add_manual",
          onPressed: () {
            if (ResponsiveUtils.isMobile(context)) {
              PlatformUtils.triggerHapticFeedback(HapticFeedbackType.light);
            }
            _showCompanyFormDialog();
          },
          icon: const Icon(Icons.add),
          label: const Text('Ajouter'),
          tooltip: 'Ajouter une entreprise manuellement',
        ),
      ],
    );
  }

  // Scanner de carte de visite avec OCR
  Future<void> _scanBusinessCard() async {
    try {
      // Afficher les options de scan
      final scanOption = await showModalBottomSheet<String>(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (context) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          ),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle bar
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.outline.withAlpha(100),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    'Scanner une carte de visite',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),

                const Divider(height: 1),

                ListTile(
                  leading: const Icon(Icons.camera_alt),
                  title: const Text('Prendre une photo'),
                  subtitle: const Text('Scanner avec l\'appareil photo'),
                  onTap: () => Navigator.pop(context, 'camera'),
                ),

                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('Choisir depuis la galerie'),
                  subtitle: const Text('Sélectionner une image existante'),
                  onTap: () => Navigator.pop(context, 'gallery'),
                ),

                ListTile(
                  leading: const Icon(Icons.edit),
                  title: const Text('Saisie manuelle'),
                  subtitle: const Text('Ajouter les informations manuellement'),
                  onTap: () => Navigator.pop(context, 'manual'),
                ),

                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      );

      if (scanOption == null) return;

      if (scanOption == 'manual') {
        _showCompanyFormDialog();
        return;
      }

      // Afficher un indicateur de chargement
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const Center(
            child: Card(
              child: Padding(
                padding: EdgeInsets.all(20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Analyse de la carte de visite...'),
                  ],
                ),
              ),
            ),
          ),
        );
      }

      // Scanner la carte de visite
      BusinessCardData? cardData;
      if (scanOption == 'camera') {
        cardData = await BusinessCardOCRService.scanFromCamera();
      } else if (scanOption == 'gallery') {
        cardData = await BusinessCardOCRService.scanFromGallery();
      }

      // Fermer l'indicateur de chargement
      if (mounted) Navigator.pop(context);

      if (cardData == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Aucune donnée extraite de la carte de visite'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      _logger.info('Données extraites: $cardData');

      // Créer un objet Company pré-rempli
      final prefilledCompany = Company(
        id: '', // Sera généré lors de la sauvegarde
        name: cardData.companyName ?? '',
        email: cardData.email,
        phoneNumber: cardData.phoneNumber,
        address: cardData.address,
        website: cardData.website,
        categories: cardData.categories,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Afficher le formulaire pré-rempli
      if (mounted) {
        _showCompanyFormDialog(initialCompany: prefilledCompany);

        // Afficher un message de succès
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Informations extraites avec succès ! ${cardData.companyName != null ? 'Entreprise: ${cardData.companyName}' : 'Vérifiez les données détectées'}'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } catch (e, stackTrace) {
      _logger.severe('Erreur lors du scan de carte de visite', e, stackTrace);

      // Fermer l'indicateur de chargement si ouvert
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du scan: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }
}
