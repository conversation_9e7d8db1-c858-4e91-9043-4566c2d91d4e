import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/company_repository.dart';
import '../models/company_model.dart';
import 'package:seqqo/core/widgets/modern_modal.dart';
import 'package:seqqo/core/widgets/app_button.dart';
import 'package:seqqo/core/utils/responsive_utils.dart';

/// Modale moderne pour créer/modifier une entreprise dans l'annuaire
class ModernCompanyDialog extends ConsumerStatefulWidget {
  final Company? initialCompany;

  const ModernCompanyDialog({super.key, this.initialCompany});

  @override
  ConsumerState<ModernCompanyDialog> createState() =>
      _ModernCompanyDialogState();
}

class _ModernCompanyDialogState extends ConsumerState<ModernCompanyDialog> {
  final _formKey = GlobalKey<FormState>();
  late bool _isEditing;
  bool _isLoading = false;
  String? _errorMessage;

  // Form controllers
  late final TextEditingController _nameController;
  late final TextEditingController _siretController;
  late final TextEditingController _phoneNumberController;
  late final TextEditingController _emailController;
  late final TextEditingController _websiteController;
  late final TextEditingController _addressController;
  late final TextEditingController _notesController;

  // Liste des catégories sélectionnées
  List<String> _selectedCategories = [];

  // Liste des catégories prédéfinies
  final List<String> _predefinedCategories = [
    'Démolition',
    'Gros œuvre',
    'Charpente',
    'Couverture',
    'Étanchéité',
    'Menuiseries extérieures',
    'Menuiseries intérieures',
    'Plâtrerie',
    'Isolation',
    'Électricité',
    'Plomberie',
    'Chauffage',
    'Ventilation',
    'Carrelage',
    'Revêtements de sols',
    'Peinture',
    'Serrurerie',
    'Espaces verts',
    'VRD',
    'Autre',
  ];

  @override
  void initState() {
    super.initState();
    _isEditing = widget.initialCompany != null;

    // Initialiser les catégories sélectionnées si on est en mode édition
    if (_isEditing && widget.initialCompany != null) {
      _selectedCategories = List.from(widget.initialCompany!.categories);
    }

    // Initialize controllers with existing data if editing
    _nameController = TextEditingController(
      text: widget.initialCompany?.name ?? '',
    );
    _siretController = TextEditingController(
      text: widget.initialCompany?.siret ?? '',
    );
    _phoneNumberController = TextEditingController(
      text: widget.initialCompany?.phoneNumber ?? '',
    );
    _emailController = TextEditingController(
      text: widget.initialCompany?.email ?? '',
    );
    _websiteController = TextEditingController(
      text: widget.initialCompany?.website ?? '',
    );
    _addressController = TextEditingController(
      text: widget.initialCompany?.address ?? '',
    );
    _notesController = TextEditingController(
      text: widget.initialCompany?.notes ?? '',
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _siretController.dispose();
    _phoneNumberController.dispose();
    _emailController.dispose();
    _websiteController.dispose();
    _addressController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _saveCompany() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final company = Company(
        id: widget.initialCompany?.id ?? '',
        name: _nameController.text.trim(),
        siret:
            _siretController.text.isEmpty ? null : _siretController.text.trim(),
        phoneNumber: _phoneNumberController.text.isEmpty
            ? null
            : _phoneNumberController.text.trim(),
        email:
            _emailController.text.isEmpty ? null : _emailController.text.trim(),
        website: _websiteController.text.isEmpty
            ? null
            : _websiteController.text.trim(),
        address: _addressController.text.isEmpty
            ? null
            : _addressController.text.trim(),
        notes:
            _notesController.text.isEmpty ? null : _notesController.text.trim(),
        categories: _selectedCategories,
        createdAt: widget.initialCompany?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (_isEditing) {
        await ref.read(companyRepositoryProvider).updateCompany(company);
      } else {
        await ref.read(companyRepositoryProvider).addCompany(company);
      }

      if (mounted) {
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isMobile = ResponsiveUtils.isMobile(context);

    return ModernModal(
      title: _isEditing ? 'Modifier l\'entreprise' : 'Nouvelle entreprise',
      maxHeight: isMobile ? null : MediaQuery.of(context).size.height * 0.85,
      actions: [
        AppButton.ghost(
          onPressed: () => Navigator.of(context).pop(),
          text: 'Annuler',
        ),
        AppButton.primary(
          onPressed: _isLoading ? null : () => _saveCompany(),
          text: _isEditing ? 'Enregistrer' : 'Créer',
          isLoading: _isLoading,
        ),
      ],
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Message d'erreur si nécessaire
            if (_errorMessage != null)
              Container(
                margin: const EdgeInsets.only(bottom: 16),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.errorContainer,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.colorScheme.error.withAlpha(60),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: theme.colorScheme.onErrorContainer,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onErrorContainer,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.close,
                        color: theme.colorScheme.onErrorContainer,
                        size: 18,
                      ),
                      onPressed: () => setState(() => _errorMessage = null),
                      style: IconButton.styleFrom(
                        minimumSize: const Size(32, 32),
                        padding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                ),
              ),

            // Section Informations principales
            _buildSectionHeader(
                theme, Icons.business, 'Informations principales'),
            const SizedBox(height: 16),

            ModernFormField(
              label: 'Nom de l\'entreprise *',
              hint: 'ex: Urbay Construction',
              controller: _nameController,
              prefixIcon: const Icon(Icons.business_outlined),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Le nom de l\'entreprise est obligatoire';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            ModernFormField(
              label: 'Numéro SIRET',
              hint: '12345678901234',
              controller: _siretController,
              prefixIcon: const Icon(Icons.confirmation_number_outlined),
              keyboardType: TextInputType.number,
              suffixIcon: const Tooltip(
                message: 'Numéro à 14 chiffres identifiant l\'entreprise',
                child: Icon(Icons.info_outline),
              ),
            ),
            const SizedBox(height: 24),

            // Section Contact
            _buildSectionHeader(theme, Icons.contact_phone, 'Contact'),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: ModernFormField(
                    label: 'Téléphone',
                    hint: '01 23 45 67 89',
                    controller: _phoneNumberController,
                    prefixIcon: const Icon(Icons.phone_outlined),
                    keyboardType: TextInputType.phone,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ModernFormField(
                    label: 'Email',
                    hint: '<EMAIL>',
                    controller: _emailController,
                    prefixIcon: const Icon(Icons.email_outlined),
                    keyboardType: TextInputType.emailAddress,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        final emailRegExp = RegExp(
                          r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                        );
                        if (!emailRegExp.hasMatch(value)) {
                          return 'Format d\'email non valide';
                        }
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            ModernFormField(
              label: 'Site Web',
              hint: 'https://www.exemple.com',
              controller: _websiteController,
              prefixIcon: const Icon(Icons.language_outlined),
              keyboardType: TextInputType.url,
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  final urlRegExp = RegExp(
                    r'^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$',
                  );
                  if (!urlRegExp.hasMatch(value)) {
                    return 'Format d\'URL non valide';
                  }
                }
                return null;
              },
            ),
            const SizedBox(height: 24),

            // Section Adresse et Notes
            _buildSectionHeader(theme, Icons.location_on, 'Adresse et Notes'),
            const SizedBox(height: 16),

            ModernFormField(
              label: 'Adresse',
              hint: 'Adresse complète de l\'entreprise',
              controller: _addressController,
              prefixIcon: const Icon(Icons.location_on_outlined),
            ),
            const SizedBox(height: 16),

            ModernFormField(
              label: 'Notes',
              hint: 'Informations complémentaires',
              controller: _notesController,
              prefixIcon: const Icon(Icons.notes_outlined),
              maxLines: 3,
            ),
            const SizedBox(height: 24),

            // Section Catégories
            _buildSectionHeader(theme, Icons.category, 'Catégories'),
            const SizedBox(height: 16),
            _buildCategoriesSection(theme),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(ThemeData theme, IconData icon, String title) {
    return Row(
      children: [
        Icon(icon, color: theme.colorScheme.primary, size: 20),
        const SizedBox(width: 8),
        Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.primary,
            letterSpacing: -0.2,
          ),
        ),
      ],
    );
  }

  Widget _buildCategoriesSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sélectionnez les domaines d\'activité de l\'entreprise',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withAlpha(160),
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _predefinedCategories.map((category) {
            final isSelected = _selectedCategories.contains(category);
            return FilterChip(
              label: Text(category),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedCategories.add(category);
                  } else {
                    _selectedCategories.remove(category);
                  }
                });
              },
              backgroundColor: theme.colorScheme.surface,
              selectedColor: theme.colorScheme.primaryContainer,
              checkmarkColor: theme.colorScheme.primary,
              side: BorderSide(
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.outline.withAlpha(60),
                width: 1,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              labelStyle: theme.textTheme.bodySmall?.copyWith(
                color: isSelected
                    ? theme.colorScheme.onPrimaryContainer
                    : theme.colorScheme.onSurface,
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
              ),
            );
          }).toList(),
        ),
        if (_selectedCategories.isNotEmpty) ...[
          const SizedBox(height: 12),
          Text(
            '${_selectedCategories.length} catégorie${_selectedCategories.length > 1 ? 's' : ''} sélectionnée${_selectedCategories.length > 1 ? 's' : ''}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ],
    );
  }
}
