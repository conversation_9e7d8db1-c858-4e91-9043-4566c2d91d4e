import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../core/utils/responsive_utils.dart';

/// Widget de footer global pour l'application
///
/// Ce footer est conçu pour être affiché en bas de chaque écran de l'application.
/// Il s'adapte automatiquement aux différentes tailles d'écran.
class AppFooter extends StatefulWidget {
  /// Indique si le footer doit être compact (pour les petits écrans)
  final bool isCompact;

  /// Crée un footer global pour l'application
  const AppFooter({
    super.key,
    this.isCompact = false,
  });

  @override
  State<AppFooter> createState() => _AppFooterState();
}

class _AppFooterState extends State<AppFooter> {
  String _appVersion = '';

  @override
  void initState() {
    super.initState();
    _loadAppVersion();
  }

  /// Charge la version de l'application
  Future<void> _loadAppVersion() async {
    try {
      final PackageInfo packageInfo = await PackageInfo.fromPlatform();
      if (mounted) {
        setState(() {
          _appVersion = packageInfo.version;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _appVersion = '';
        });
      }
    }
  }

  /// Lance une URL dans le navigateur
  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch $url');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isSmallScreen = ResponsiveUtils.isMobile(context);

    // Adapter le style en fonction de la taille de l'écran
    final double fontSize = isSmallScreen || widget.isCompact ? 11.0 : 12.0;
    final double iconSize = isSmallScreen || widget.isCompact ? 14.0 : 16.0;
    final double verticalPadding =
        isSmallScreen || widget.isCompact ? 8.0 : 12.0;
    final double horizontalPadding =
        isSmallScreen || widget.isCompact ? 16.0 : 24.0;

    // Couleur du texte légèrement atténuée pour un style discret
    final Color textColor = colorScheme.onSurfaceVariant.withOpacity(0.8);

    // Style de texte commun pour tous les éléments du footer
    final TextStyle footerTextStyle = TextStyle(
      fontSize: fontSize,
      color: textColor,
      fontFamily: 'RedditSans',
    );

    // Style pour les liens
    final TextStyle linkStyle = footerTextStyle.copyWith(
      color: colorScheme.primary.withOpacity(0.8),
      decoration: TextDecoration.underline,
      decorationColor: colorScheme.primary.withOpacity(0.4),
    );

    // Construire le footer en fonction de la taille de l'écran
    if (isSmallScreen || widget.isCompact) {
      // Version mobile : masquer complètement le footer
      return const SizedBox.shrink();
    } else {
      // Version complète pour desktop
      return Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(
          vertical: verticalPadding,
          horizontal: horizontalPadding,
        ),
        decoration: BoxDecoration(
          color: colorScheme.surface,
          border: Border(
            top: BorderSide(
              color: colorScheme.outlineVariant.withOpacity(0.5),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Partie gauche: copyright
            Text(
              'Seqqo, créé par Anthony Dutertre © 2025',
              style: footerTextStyle,
            ),

            // Partie centrale: liens
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                InkWell(
                  onTap: () => _launchURL('https://seqqo.com/terms'),
                  child: Text('Conditions d\'utilisation', style: linkStyle),
                ),
                Text(' • ', style: footerTextStyle),
                InkWell(
                  onTap: () => _launchURL('https://seqqo.com/privacy'),
                  child: Text('Politique de confidentialité', style: linkStyle),
                ),
                Text(' • ', style: footerTextStyle),
                InkWell(
                  onTap: () => _launchURL('https://seqqo.com/help'),
                  child: Text('Aide', style: linkStyle),
                ),
              ],
            ),

            // Partie droite: version et réseaux sociaux
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_appVersion.isNotEmpty)
                  Text('v$_appVersion', style: footerTextStyle),
                const SizedBox(width: 12),
                IconButton(
                  icon: Icon(Icons.mail_outline, size: iconSize),
                  onPressed: () => _launchURL('mailto:<EMAIL>'),
                  tooltip: 'Contact',
                  color: textColor,
                  padding: EdgeInsets.zero,
                  constraints: BoxConstraints(
                    minWidth: iconSize * 1.5,
                    minHeight: iconSize * 1.5,
                  ),
                  visualDensity: VisualDensity.compact,
                ),
              ],
            ),
          ],
        ),
      );
    }
  }
}
