import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';

/// Script pour générer automatiquement toutes les icônes Seqqo
/// aux tailles requises pour Android et iOS
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🎨 Génération des icônes Seqqo...');
  
  final iconGenerator = SeqqoIconGenerator();
  await iconGenerator.generateAllIcons();
  
  print('✅ Toutes les icônes ont été générées avec succès !');
}

class SeqqoIconGenerator {
  /// Tailles requises pour Android
  static const Map<String, int> androidSizes = {
    'mipmap-mdpi': 48,
    'mipmap-hdpi': 72,
    'mipmap-xhdpi': 96,
    'mipmap-xxhdpi': 144,
    'mipmap-xxxhdpi': 192,
  };

  /// Tailles requises pour iOS
  static const Map<String, int> iosSizes = {
    'Icon-App-20x20@1x': 20,
    'Icon-App-20x20@2x': 40,
    'Icon-App-20x20@3x': 60,
    'Icon-App-29x29@1x': 29,
    'Icon-App-29x29@2x': 58,
    'Icon-App-29x29@3x': 87,
    'Icon-App-40x40@1x': 40,
    'Icon-App-40x40@2x': 80,
    'Icon-App-40x40@3x': 120,
    'Icon-App-50x50@1x': 50,
    'Icon-App-50x50@2x': 100,
    'Icon-App-57x57@1x': 57,
    'Icon-App-57x57@2x': 114,
    'Icon-App-60x60@2x': 120,
    'Icon-App-60x60@3x': 180,
    'Icon-App-72x72@1x': 72,
    'Icon-App-72x72@2x': 144,
    'Icon-App-76x76@1x': 76,
    'Icon-App-76x76@2x': 152,
    'Icon-App-83.5x83.5@2x': 167,
    'Icon-App-1024x1024@1x': 1024,
  };

  /// Génère toutes les icônes pour Android et iOS
  Future<void> generateAllIcons() async {
    // Créer les répertoires si nécessaire
    await _createDirectories();
    
    // Générer les icônes Android
    await _generateAndroidIcons();
    
    // Générer les icônes iOS
    await _generateIOSIcons();
    
    print('📱 Icônes générées pour Android et iOS');
  }

  /// Crée les répertoires nécessaires
  Future<void> _createDirectories() async {
    // Répertoires Android
    for (final folder in androidSizes.keys) {
      final dir = Directory('android/app/src/main/res/$folder');
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }
    }

    // Répertoire iOS
    final iosDir = Directory('ios/Runner/Assets.xcassets/AppIcon.appiconset');
    if (!await iosDir.exists()) {
      await iosDir.create(recursive: true);
    }
  }

  /// Génère les icônes pour Android
  Future<void> _generateAndroidIcons() async {
    print('🤖 Génération des icônes Android...');
    
    for (final entry in androidSizes.entries) {
      final folder = entry.key;
      final size = entry.value;
      
      final iconData = await _generateSeqqoIcon(size);
      final file = File('android/app/src/main/res/$folder/ic_launcher.png');
      await file.writeAsBytes(iconData);
      
      print('  ✓ $folder/ic_launcher.png (${size}x$size)');
    }
  }

  /// Génère les icônes pour iOS
  Future<void> _generateIOSIcons() async {
    print('🍎 Génération des icônes iOS...');
    
    for (final entry in iosSizes.entries) {
      final filename = entry.key;
      final size = entry.value;
      
      final iconData = await _generateSeqqoIcon(size);
      final file = File('ios/Runner/Assets.xcassets/AppIcon.appiconset/$filename.png');
      await file.writeAsBytes(iconData);
      
      print('  ✓ $filename.png (${size}x$size)');
    }
  }

  /// Génère une icône Seqqo à la taille spécifiée
  Future<Uint8List> _generateSeqqoIcon(int size) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    
    // Dessiner l'icône Seqqo
    _drawSeqqoIcon(canvas, size.toDouble());
    
    final picture = recorder.endRecording();
    final image = await picture.toImage(size, size);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    
    return byteData!.buffer.asUint8List();
  }

  /// Dessine l'icône Seqqo sur le canvas
  void _drawSeqqoIcon(Canvas canvas, double size) {
    // Fond sombre avec coins arrondis
    final backgroundPaint = Paint()
      ..color = const Color(0xFF2D2D2D)
      ..style = PaintingStyle.fill;
    
    final backgroundRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, size, size),
      Radius.circular(size * 0.25), // 25% de rayon pour les coins arrondis
    );
    canvas.drawRRect(backgroundRect, backgroundPaint);

    // Lettre S stylisée
    final sPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = size * 0.08 // 8% de la taille
      ..strokeCap = StrokeCap.round;

    final path = Path();
    final margin = size * 0.2; // 20% de marge
    final innerSize = size - (margin * 2);
    final centerX = size / 2;
    final centerY = size / 2;
    
    // Dessiner le S
    final topY = margin;
    final middleY = centerY;
    final bottomY = size - margin;
    final leftX = margin;
    final rightX = size - margin;
    
    // Partie haute du S (de droite à gauche)
    path.moveTo(rightX, topY + innerSize * 0.15);
    path.lineTo(leftX + innerSize * 0.15, topY + innerSize * 0.15);
    path.quadraticBezierTo(leftX, topY + innerSize * 0.15, leftX, topY + innerSize * 0.25);
    path.lineTo(leftX, middleY - innerSize * 0.1);
    path.quadraticBezierTo(leftX, middleY, leftX + innerSize * 0.15, middleY);
    
    // Partie centrale
    path.lineTo(rightX - innerSize * 0.15, middleY);
    path.quadraticBezierTo(rightX, middleY, rightX, middleY + innerSize * 0.1);
    
    // Partie basse du S (de droite à gauche)
    path.lineTo(rightX, bottomY - innerSize * 0.25);
    path.quadraticBezierTo(rightX, bottomY - innerSize * 0.15, rightX - innerSize * 0.15, bottomY - innerSize * 0.15);
    path.lineTo(leftX, bottomY - innerSize * 0.15);
    
    canvas.drawPath(path, sPaint);
  }
}
