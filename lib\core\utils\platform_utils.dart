import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Utilitaires pour la détection et l'adaptation aux plateformes
class PlatformUtils {
  /// Détermine si l'application s'exécute sur Android
  static bool get isAndroid => defaultTargetPlatform == TargetPlatform.android;

  /// Détermine si l'application s'exécute sur iOS
  static bool get isIOS => defaultTargetPlatform == TargetPlatform.iOS;

  /// Détermine si l'application s'exécute sur une plateforme mobile
  static bool get isMobile => isAndroid || isIOS;

  /// Détermine si l'application s'exécute sur le web
  static bool get isWeb => kIsWeb;

  /// Détermine si l'application s'exécute sur desktop (Windows, macOS, Linux)
  static bool get isDesktop =>
      defaultTargetPlatform == TargetPlatform.windows ||
      defaultTargetPlatform == TargetPlatform.macOS ||
      defaultTargetPlatform == TargetPlatform.linux;

  /// Détermine si l'application s'exécute sur une vraie plateforme mobile (pas web)
  static bool get isNativeMobile => !kIsWeb && isMobile;

  /// Détermine si l'application doit utiliser les optimisations mobiles
  static bool shouldUseMobileOptimizations(BuildContext context) {
    // Toujours utiliser les optimisations sur les plateformes mobiles natives
    if (isNativeMobile) return true;

    // Sur le web, utiliser les optimisations si l'écran est petit
    if (isWeb) {
      return MediaQuery.of(context).size.width < 600;
    }

    return false;
  }

  /// Retourne le nom de la plateforme actuelle
  static String get platformName {
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return 'Android';
      case TargetPlatform.iOS:
        return 'iOS';
      case TargetPlatform.windows:
        return 'Windows';
      case TargetPlatform.macOS:
        return 'macOS';
      case TargetPlatform.linux:
        return 'Linux';
      default:
        return 'Unknown';
    }
  }

  /// Retourne les ScrollPhysics appropriées pour la plateforme
  static ScrollPhysics getScrollPhysics() {
    if (isIOS) {
      return const BouncingScrollPhysics();
    } else if (isAndroid) {
      return const ClampingScrollPhysics();
    } else {
      return const ClampingScrollPhysics();
    }
  }

  /// Retourne les ScrollPhysics avec AlwaysScrollable pour la plateforme
  static ScrollPhysics getAlwaysScrollablePhysics() {
    if (isIOS) {
      return const BouncingScrollPhysics(
        parent: AlwaysScrollableScrollPhysics(),
      );
    } else if (isAndroid) {
      return const ClampingScrollPhysics(
        parent: AlwaysScrollableScrollPhysics(),
      );
    } else {
      return const ClampingScrollPhysics(
        parent: AlwaysScrollableScrollPhysics(),
      );
    }
  }

  /// Déclenche un feedback haptique approprié pour la plateforme
  static void triggerHapticFeedback(HapticFeedbackType type) {
    if (isMobile) {
      switch (type) {
        case HapticFeedbackType.light:
          HapticFeedback.lightImpact();
          break;
        case HapticFeedbackType.medium:
          HapticFeedback.mediumImpact();
          break;
        case HapticFeedbackType.heavy:
          HapticFeedback.heavyImpact();
          break;
        case HapticFeedbackType.selection:
          HapticFeedback.selectionClick();
          break;
        case HapticFeedbackType.vibrate:
          HapticFeedback.vibrate();
          break;
      }
    }
  }

  /// Retourne la durée d'animation appropriée pour la plateforme
  static Duration getAnimationDuration({bool isLong = false}) {
    if (isMobile) {
      return isLong
          ? const Duration(milliseconds: 300)
          : const Duration(milliseconds: 200);
    } else {
      return isLong
          ? const Duration(milliseconds: 400)
          : const Duration(milliseconds: 250);
    }
  }

  /// Retourne la courbe d'animation appropriée pour la plateforme
  static Curve getAnimationCurve() {
    if (isIOS) {
      return Curves.easeInOut;
    } else if (isAndroid) {
      return Curves.fastOutSlowIn;
    } else {
      return Curves.easeInOut;
    }
  }

  /// Retourne le BorderRadius approprié pour la plateforme
  static BorderRadius getBorderRadius({bool isLarge = false}) {
    if (isIOS) {
      return BorderRadius.circular(isLarge ? 16.0 : 12.0);
    } else if (isAndroid) {
      return BorderRadius.circular(isLarge ? 12.0 : 8.0);
    } else {
      return BorderRadius.circular(isLarge ? 8.0 : 4.0);
    }
  }

  /// Retourne l'élévation appropriée pour la plateforme
  static double getElevation({bool isHigh = false}) {
    if (isAndroid) {
      return isHigh ? 8.0 : 4.0;
    } else {
      return 0.0; // iOS et autres plateformes utilisent des ombres personnalisées
    }
  }

  /// Retourne les BoxShadow appropriées pour la plateforme
  static List<BoxShadow> getBoxShadow({bool isHigh = false}) {
    if (isIOS) {
      return [
        BoxShadow(
          color: Colors.black.withValues(alpha: isHigh ? 0.15 : 0.1),
          blurRadius: isHigh ? 10.0 : 5.0,
          offset: Offset(0, isHigh ? 4.0 : 2.0),
        ),
      ];
    } else if (isAndroid) {
      return [
        BoxShadow(
          color: Colors.black.withValues(alpha: isHigh ? 0.2 : 0.15),
          blurRadius: isHigh ? 8.0 : 4.0,
          offset: Offset(0, isHigh ? 3.0 : 1.5),
        ),
      ];
    } else {
      return [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.1),
          blurRadius: 4.0,
          offset: const Offset(0, 2.0),
        ),
      ];
    }
  }

  /// Retourne la taille minimale des cibles tactiles pour la plateforme
  static double getMinTouchTargetSize() {
    if (isIOS) {
      return 44.0; // Guidelines iOS
    } else if (isAndroid) {
      return 48.0; // Guidelines Material Design
    } else {
      return 44.0; // Valeur par défaut
    }
  }

  /// Retourne l'espacement minimal entre les cibles tactiles
  static double getMinTouchTargetSpacing() {
    return 8.0; // Valeur universelle
  }

  /// Détermine si le mode sombre est activé
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }

  /// Retourne la couleur d'arrière-plan appropriée pour la plateforme
  static Color getBackgroundColor(BuildContext context) {
    final isDark = isDarkMode(context);

    if (isIOS) {
      return isDark ? const Color(0xFF000000) : const Color(0xFFF2F2F7);
    } else if (isAndroid) {
      return isDark ? const Color(0xFF121212) : const Color(0xFFFAFAFA);
    } else {
      return Theme.of(context).scaffoldBackgroundColor;
    }
  }

  /// Retourne la couleur de surface appropriée pour la plateforme
  static Color getSurfaceColor(BuildContext context) {
    final isDark = isDarkMode(context);

    if (isIOS) {
      return isDark ? const Color(0xFF1C1C1E) : const Color(0xFFFFFFFF);
    } else if (isAndroid) {
      return isDark ? const Color(0xFF1E1E1E) : const Color(0xFFFFFFFF);
    } else {
      return Theme.of(context).cardColor;
    }
  }
}

/// Types de feedback haptique disponibles
enum HapticFeedbackType {
  light,
  medium,
  heavy,
  selection,
  vibrate,
}
