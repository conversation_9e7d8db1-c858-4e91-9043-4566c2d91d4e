import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';

/// Générateur d'icônes Seqqo
/// Utilise Flutter pour créer l'icône de l'application
class SeqqoIconGenerator {
  /// Génère l'icône Seqqo et la sauvegarde
  static Future<void> generateIcon() async {
    try {
      print('🎨 Génération de l\'icône Seqqo...');
      
      // Créer l'icône
      final iconData = await _createSeqqoIcon(1024);
      
      // Créer le répertoire s'il n'existe pas
      final directory = Directory('assets/icons');
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
      
      // Sauvegarder l'icône
      final file = File('assets/icons/seqqo_icon.png');
      await file.writeAsBytes(iconData);
      
      print('✅ Icône générée avec succès : ${file.path}');
      print('📱 Exécutez maintenant : dart run flutter_launcher_icons');
      
    } catch (e) {
      print('❌ Erreur lors de la génération : $e');
    }
  }
  
  /// Crée l'icône Seqqo à la taille spécifiée
  static Future<Uint8List> _createSeqqoIcon(int size) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    
    // Dessiner l'icône
    _drawSeqqoIcon(canvas, size.toDouble());
    
    // Convertir en image
    final picture = recorder.endRecording();
    final image = await picture.toImage(size, size);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    
    return byteData!.buffer.asUint8List();
  }
  
  /// Dessine l'icône Seqqo sur le canvas
  static void _drawSeqqoIcon(Canvas canvas, double size) {
    // Fond avec coins arrondis
    final backgroundPaint = Paint()
      ..color = const Color(0xFF2D2D2D)
      ..style = PaintingStyle.fill;
    
    final backgroundRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, size, size),
      Radius.circular(size * 0.25), // 25% de rayon
    );
    canvas.drawRRect(backgroundRect, backgroundPaint);
    
    // Créer un TextPainter pour dessiner le "S"
    final textStyle = TextStyle(
      color: Colors.white,
      fontSize: size * 0.55, // 55% de la taille
      fontWeight: FontWeight.w900,
      fontFamily: 'Arial',
    );
    
    final textSpan = TextSpan(
      text: 'S',
      style: textStyle,
    );
    
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    
    textPainter.layout();
    
    // Centrer le texte
    final offset = Offset(
      (size - textPainter.width) / 2,
      (size - textPainter.height) / 2,
    );
    
    textPainter.paint(canvas, offset);
  }
}

/// Widget pour prévisualiser l'icône
class SeqqoIconPreview extends StatelessWidget {
  final double size;
  
  const SeqqoIconPreview({
    super.key,
    this.size = 200,
  });
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: const Color(0xFF2D2D2D),
        borderRadius: BorderRadius.circular(size * 0.25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Center(
        child: Text(
          'S',
          style: TextStyle(
            color: Colors.white,
            fontSize: size * 0.55,
            fontWeight: FontWeight.w900,
            fontFamily: 'Arial',
          ),
        ),
      ),
    );
  }
}

/// Page de test pour l'icône
class IconGeneratorTestPage extends StatelessWidget {
  const IconGeneratorTestPage({super.key});
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Générateur d\'icône Seqqo'),
        backgroundColor: const Color(0xFF2D2D2D),
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              '🎨 Aperçu de l\'icône Seqqo',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 32),
            
            // Aperçu de l'icône
            const SeqqoIconPreview(size: 200),
            
            const SizedBox(height: 32),
            
            // Bouton pour générer
            ElevatedButton.icon(
              onPressed: () async {
                await SeqqoIconGenerator.generateIcon();
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Icône générée avec succès !'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
              icon: const Icon(Icons.download),
              label: const Text('Générer l\'icône'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2D2D2D),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Instructions
            Container(
              margin: const EdgeInsets.all(24),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Instructions :',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text('1. Cliquez sur "Générer l\'icône"'),
                  Text('2. Exécutez : flutter pub get'),
                  Text('3. Exécutez : dart run flutter_launcher_icons'),
                  Text('4. L\'icône sera appliquée à l\'application'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
