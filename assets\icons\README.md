# 🎨 Icônes Seqqo

Ce dossier contient les icônes de l'application Seqqo.

## 📱 Génération des Icônes

### Méthode 1 : Générateur HTML (Recommandé)

1. Ou<PERSON><PERSON> le fichier `scripts/create_icon_simple.html` dans votre navigateur
2. C<PERSON>z sur "📥 Télécharger 1024x1024" 
3. Renommez le fichier téléchargé en `seqqo_icon.png`
4. Placez-le dans ce dossier (`assets/icons/`)

### Méthode 2 : Script Python (Si Python est installé)

```bash
# Installer les dépendances
pip install Pillow

# Exécuter le script
python scripts/create_seqqo_icon.py
```

### Méthode 3 : Création manuelle

Si vous avez un logiciel de design (Photoshop, GIMP, Figma, etc.) :

1. Créez une image 1024x1024 pixels
2. Fond : `#2D2D2D` avec coins arrondis (rayon 256px)
3. Texte : "S" en Arial Black, blanc, centré, taille ~560px
4. Exportez en PNG sous le nom `seqqo_icon.png`

## 🚀 Installation des Icônes

Une fois que vous avez `seqqo_icon.png` dans ce dossier :

```bash
# 1. Installer les dépendances
flutter pub get

# 2. Générer les icônes pour toutes les plateformes
dart run flutter_launcher_icons

# 3. (Optionnel) Nettoyer et rebuilder
flutter clean
flutter pub get
```

## 📋 Spécifications Techniques

### Design
- **Couleur de fond** : `#2D2D2D` (gris foncé)
- **Couleur du texte** : `#FFFFFF` (blanc)
- **Police** : Arial Black ou équivalent
- **Coins arrondis** : 25% du rayon (256px pour 1024x1024)

### Tailles générées automatiquement

#### Android
- `mipmap-mdpi/ic_launcher.png` (48x48)
- `mipmap-hdpi/ic_launcher.png` (72x72)
- `mipmap-xhdpi/ic_launcher.png` (96x96)
- `mipmap-xxhdpi/ic_launcher.png` (144x144)
- `mipmap-xxxhdpi/ic_launcher.png` (192x192)

#### iOS
- `<EMAIL>` (20x20)
- `<EMAIL>` (40x40)
- `<EMAIL>` (60x60)
- `<EMAIL>` (29x29)
- `<EMAIL>` (58x58)
- `<EMAIL>` (87x87)
- `<EMAIL>` (40x40)
- `<EMAIL>` (80x80)
- `<EMAIL>` (120x120)
- `<EMAIL>` (120x120)
- `<EMAIL>` (180x180)
- `<EMAIL>` (76x76)
- `<EMAIL>` (152x152)
- `<EMAIL>` (167x167)
- `<EMAIL>` (1024x1024)

#### Web, Windows, macOS
- Icônes adaptées automatiquement selon les spécifications de chaque plateforme

## 🔧 Configuration

La configuration des icônes se trouve dans `pubspec.yaml` :

```yaml
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/seqqo_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icons/seqqo_icon.png"
    background_color: "#2D2D2D"
    theme_color: "#2D2D2D"
  windows:
    generate: true
    image_path: "assets/icons/seqqo_icon.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/icons/seqqo_icon.png"
```

## ✅ Vérification

Après génération, vérifiez que les icônes ont été créées :

- **Android** : `android/app/src/main/res/mipmap-*/ic_launcher.png`
- **iOS** : `ios/Runner/Assets.xcassets/AppIcon.appiconset/*.png`

## 🎯 Résultat

L'icône Seqqo apparaîtra sur l'écran d'accueil de tous les appareils avec :
- Un fond gris foncé moderne
- La lettre "S" blanche bien visible
- Des coins arrondis élégants
- Une qualité parfaite sur tous les écrans (du plus petit au plus grand)

## 🔄 Mise à jour

Pour changer l'icône :
1. Remplacez `seqqo_icon.png` par votre nouvelle icône
2. Relancez `dart run flutter_launcher_icons`
3. Testez sur les appareils cibles
