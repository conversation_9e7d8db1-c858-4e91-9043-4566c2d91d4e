import 'package:flutter/material.dart';
import 'package:seqqo/core/utils/platform_utils.dart';
import 'package:seqqo/core/utils/responsive_utils.dart';

/// Indicateur de rafraîchissement moderne avec design Seqqo
class ModernRefreshIndicator extends StatelessWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final Color? color;
  final Color? backgroundColor;
  final double displacement;
  final double strokeWidth;

  const ModernRefreshIndicator({
    super.key,
    required this.child,
    required this.onRefresh,
    this.color,
    this.backgroundColor,
    this.displacement = 40.0,
    this.strokeWidth = 2.5,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return RefreshIndicator(
      onRefresh: () async {
        if (ResponsiveUtils.isMobile(context)) {
          PlatformUtils.triggerHapticFeedback(HapticFeedbackType.medium);
        }
        await onRefresh();
      },
      color: color ?? theme.colorScheme.primary,
      backgroundColor: backgroundColor ?? theme.colorScheme.surface,
      displacement: displacement,
      strokeWidth: strokeWidth,
      child: child,
    );
  }
}

/// Widget de rafraîchissement personnalisé avec animation
class CustomRefreshIndicator extends StatefulWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final double triggerDistance;
  final Widget Function(BuildContext context, double progress)? builder;

  const CustomRefreshIndicator({
    super.key,
    required this.child,
    required this.onRefresh,
    this.triggerDistance = 80.0,
    this.builder,
  });

  @override
  State<CustomRefreshIndicator> createState() => _CustomRefreshIndicatorState();
}

class _CustomRefreshIndicatorState extends State<CustomRefreshIndicator>
    with TickerProviderStateMixin {
  late AnimationController _refreshController;
  late AnimationController _scaleController;
  bool _isRefreshing = false;
  double _dragDistance = 0.0;

  @override
  void initState() {
    super.initState();
    _refreshController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _refreshController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: _handleScrollNotification,
      child: Stack(
        children: [
          widget.child,
          if (_dragDistance > 0)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: _buildRefreshIndicator(context),
            ),
        ],
      ),
    );
  }

  bool _handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollUpdateNotification) {
      if (notification.metrics.pixels < 0 && !_isRefreshing) {
        setState(() {
          _dragDistance = (-notification.metrics.pixels).clamp(0.0, widget.triggerDistance * 1.5);
        });
        
        final progress = _dragDistance / widget.triggerDistance;
        _scaleController.value = progress.clamp(0.0, 1.0);
      }
    } else if (notification is ScrollEndNotification) {
      if (_dragDistance >= widget.triggerDistance && !_isRefreshing) {
        _triggerRefresh();
      } else {
        _resetDrag();
      }
    }
    return false;
  }

  Widget _buildRefreshIndicator(BuildContext context) {
    final theme = Theme.of(context);
    final progress = (_dragDistance / widget.triggerDistance).clamp(0.0, 1.0);
    
    if (widget.builder != null) {
      return widget.builder!(context, progress);
    }
    
    return Container(
      height: _dragDistance.clamp(0.0, 80.0),
      alignment: Alignment.center,
      child: AnimatedBuilder(
        animation: _scaleController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleController.value,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: theme.colorScheme.shadow.withValues(alpha: 0.1),
                    offset: const Offset(0, 2),
                    blurRadius: 8,
                  ),
                ],
              ),
              child: _isRefreshing
                  ? AnimatedBuilder(
                      animation: _refreshController,
                      builder: (context, child) {
                        return Transform.rotate(
                          angle: _refreshController.value * 2 * 3.14159,
                          child: Icon(
                            Icons.refresh_rounded,
                            color: theme.colorScheme.primary,
                            size: 20,
                          ),
                        );
                      },
                    )
                  : Transform.rotate(
                      angle: progress * 3.14159,
                      child: Icon(
                        Icons.arrow_downward_rounded,
                        color: theme.colorScheme.primary,
                        size: 20,
                      ),
                    ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _triggerRefresh() async {
    if (_isRefreshing) return;
    
    setState(() {
      _isRefreshing = true;
    });
    
    if (ResponsiveUtils.isMobile(context)) {
      PlatformUtils.triggerHapticFeedback(HapticFeedbackType.medium);
    }
    
    _refreshController.repeat();
    
    try {
      await widget.onRefresh();
    } finally {
      _refreshController.stop();
      _refreshController.reset();
      _resetDrag();
      setState(() {
        _isRefreshing = false;
      });
    }
  }

  void _resetDrag() {
    setState(() {
      _dragDistance = 0.0;
    });
    _scaleController.reverse();
  }
}

/// Indicateur de rafraîchissement avec message personnalisé
class MessageRefreshIndicator extends StatelessWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final String message;
  final IconData? icon;

  const MessageRefreshIndicator({
    super.key,
    required this.child,
    required this.onRefresh,
    this.message = 'Tirez pour actualiser',
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return CustomRefreshIndicator(
      onRefresh: onRefresh,
      builder: (context, progress) {
        final theme = Theme.of(context);
        final opacity = progress.clamp(0.0, 1.0);
        
        return Container(
          height: 80 * progress,
          alignment: Alignment.center,
          child: Opacity(
            opacity: opacity,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (icon != null)
                  Icon(
                    icon,
                    color: theme.colorScheme.primary,
                    size: 24,
                  ),
                const SizedBox(height: 8),
                Text(
                  message,
                  style: TextStyle(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        );
      },
      child: child,
    );
  }
}

/// Wrapper pour les listes avec pull-to-refresh automatique
class RefreshableList extends StatelessWidget {
  final Future<void> Function() onRefresh;
  final Widget Function(BuildContext context) builder;
  final String? refreshMessage;
  final bool useCustomIndicator;

  const RefreshableList({
    super.key,
    required this.onRefresh,
    required this.builder,
    this.refreshMessage,
    this.useCustomIndicator = false,
  });

  @override
  Widget build(BuildContext context) {
    final child = builder(context);
    
    if (useCustomIndicator) {
      return MessageRefreshIndicator(
        onRefresh: onRefresh,
        message: refreshMessage ?? 'Tirez pour actualiser',
        icon: Icons.refresh_rounded,
        child: child,
      );
    }
    
    return ModernRefreshIndicator(
      onRefresh: onRefresh,
      child: child,
    );
  }
}
