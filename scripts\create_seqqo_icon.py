#!/usr/bin/env python3
"""
Script pour créer l'icône Seqqo
Nécessite: pip install Pillow
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_seqqo_icon():
    """Crée l'icône Seqqo avec un S blanc sur fond sombre"""
    
    # Taille de l'icône (1024x1024 pour la meilleure qualité)
    size = 1024
    
    # Créer une nouvelle image avec fond transparent
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Couleurs
    bg_color = (45, 45, 45, 255)  # #2D2D2D
    text_color = (255, 255, 255, 255)  # Blanc
    
    # Dessiner le fond avec coins arrondis
    corner_radius = size // 4  # 25% de rayon
    
    # Créer un rectangle avec coins arrondis
    draw.rounded_rectangle(
        [(0, 0), (size, size)],
        radius=corner_radius,
        fill=bg_color
    )
    
    # Essayer de charger une police système
    try:
        # Taille de police (environ 55% de la taille de l'icône)
        font_size = int(size * 0.55)
        
        # Essayer différentes polices selon l'OS
        font_paths = [
            "/System/Library/Fonts/Arial Black.ttf",  # macOS
            "/Windows/Fonts/ariblk.ttf",  # Windows
            "/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf",  # Linux
            "/usr/share/fonts/TTF/arial.ttf",  # Linux alternatif
        ]
        
        font = None
        for font_path in font_paths:
            if os.path.exists(font_path):
                try:
                    font = ImageFont.truetype(font_path, font_size)
                    break
                except:
                    continue
        
        # Si aucune police trouvée, utiliser la police par défaut
        if font is None:
            font = ImageFont.load_default()
            
    except Exception as e:
        print(f"Erreur lors du chargement de la police: {e}")
        font = ImageFont.load_default()
    
    # Texte à dessiner
    text = "S"
    
    # Calculer la position pour centrer le texte
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size - text_width) // 2
    y = (size - text_height) // 2 - bbox[1]  # Ajuster pour l'offset de la police
    
    # Dessiner le texte
    draw.text((x, y), text, fill=text_color, font=font)
    
    # Créer le répertoire de destination s'il n'existe pas
    os.makedirs('assets/icons', exist_ok=True)
    
    # Sauvegarder l'icône
    output_path = 'assets/icons/seqqo_icon.png'
    img.save(output_path, 'PNG')
    
    print(f"✅ Icône Seqqo créée: {output_path}")
    print(f"   Taille: {size}x{size} pixels")
    print(f"   Couleur de fond: {bg_color}")
    print(f"   Couleur du texte: {text_color}")
    
    return output_path

def create_multiple_sizes():
    """Crée l'icône en plusieurs tailles pour différents usages"""
    
    sizes = [16, 32, 48, 72, 96, 144, 192, 512, 1024]
    
    for size in sizes:
        create_seqqo_icon_size(size)

def create_seqqo_icon_size(size):
    """Crée une icône Seqqo à une taille spécifique"""
    
    # Créer une nouvelle image
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Couleurs
    bg_color = (45, 45, 45, 255)
    text_color = (255, 255, 255, 255)
    
    # Coins arrondis proportionnels
    corner_radius = max(1, size // 4)
    
    # Fond avec coins arrondis
    draw.rounded_rectangle(
        [(0, 0), (size, size)],
        radius=corner_radius,
        fill=bg_color
    )
    
    # Police proportionnelle
    font_size = max(8, int(size * 0.55))
    
    try:
        font = ImageFont.load_default()
    except:
        font = None
    
    # Texte
    text = "S"
    
    if font:
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size - text_width) // 2
        y = (size - text_height) // 2 - bbox[1]
        
        draw.text((x, y), text, fill=text_color, font=font)
    else:
        # Fallback: dessiner un rectangle simple
        margin = size // 4
        draw.rectangle(
            [(margin, margin), (size - margin, size - margin)],
            fill=text_color
        )
    
    # Sauvegarder
    output_path = f'assets/icons/seqqo_icon_{size}.png'
    img.save(output_path, 'PNG')
    
    print(f"✅ Icône {size}x{size} créée: {output_path}")

if __name__ == "__main__":
    print("🎨 Création de l'icône Seqqo...")
    
    # Créer l'icône principale
    create_seqqo_icon()
    
    # Créer les tailles multiples (optionnel)
    # create_multiple_sizes()
    
    print("\n📱 Pour appliquer les icônes:")
    print("1. Exécutez: flutter pub get")
    print("2. Exécutez: dart run flutter_launcher_icons")
    print("3. Les icônes seront automatiquement générées pour Android et iOS")
