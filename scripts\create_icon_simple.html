<!DOCTYPE html>
<html>
<head>
    <title>Générateur d'i<PERSON>ône Seqqo</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        .icon-preview {
            width: 512px;
            height: 512px;
            background: #2D2D2D;
            border-radius: 128px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .icon-text {
            color: white;
            font-family: 'Arial Black', Arial, sans-serif;
            font-size: 280px;
            font-weight: 900;
            line-height: 1;
            user-select: none;
        }
        .download-section {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        .btn {
            background: #2D2D2D;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.2s;
        }
        .btn:hover {
            background: #404040;
        }
        .instructions {
            text-align: left;
            max-width: 600px;
            margin: 0 auto;
        }
        .size-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .size-item {
            text-align: center;
        }
        .size-icon {
            background: #2D2D2D;
            border-radius: 25%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Générateur d'icône Seqqo</h1>
        
        <div class="icon-preview" id="iconPreview">
            <div class="icon-text">S</div>
        </div>
        
        <div class="download-section">
            <h2>Télécharger l'icône</h2>
            <button class="btn" onclick="downloadIcon(1024)">📥 Télécharger 1024x1024</button>
            <button class="btn" onclick="downloadIcon(512)">📥 Télécharger 512x512</button>
            <button class="btn" onclick="downloadAllSizes()">📦 Télécharger toutes les tailles</button>
            
            <div class="instructions">
                <h3>Instructions d'installation :</h3>
                <ol>
                    <li>Téléchargez l'icône 1024x1024</li>
                    <li>Renommez le fichier en <code>seqqo_icon.png</code></li>
                    <li>Placez-le dans le dossier <code>assets/icons/</code> de votre projet</li>
                    <li>Exécutez <code>flutter pub get</code></li>
                    <li>Exécutez <code>dart run flutter_launcher_icons</code></li>
                </ol>
            </div>
        </div>
        
        <div class="download-section">
            <h3>Aperçu des tailles</h3>
            <div class="size-grid">
                <div class="size-item">
                    <div class="size-icon" style="width: 16px; height: 16px; font-size: 8px;">S</div>
                    <div>16x16</div>
                </div>
                <div class="size-item">
                    <div class="size-icon" style="width: 32px; height: 32px; font-size: 16px;">S</div>
                    <div>32x32</div>
                </div>
                <div class="size-item">
                    <div class="size-icon" style="width: 48px; height: 48px; font-size: 24px;">S</div>
                    <div>48x48</div>
                </div>
                <div class="size-item">
                    <div class="size-icon" style="width: 72px; height: 72px; font-size: 36px;">S</div>
                    <div>72x72</div>
                </div>
                <div class="size-item">
                    <div class="size-icon" style="width: 96px; height: 96px; font-size: 48px;">S</div>
                    <div>96x96</div>
                </div>
                <div class="size-item">
                    <div class="size-icon" style="width: 144px; height: 144px; font-size: 72px;">S</div>
                    <div>144x144</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function createIconCanvas(size) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = size;
            canvas.height = size;
            
            // Fond avec coins arrondis
            const cornerRadius = size * 0.25;
            ctx.fillStyle = '#2D2D2D';
            ctx.beginPath();
            roundRect(ctx, 0, 0, size, size, cornerRadius);
            ctx.fill();
            
            // Lettre S
            ctx.fillStyle = 'white';
            ctx.font = `900 ${size * 0.55}px Arial Black, Arial, sans-serif`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('S', size / 2, size / 2);
            
            return canvas;
        }
        
        function roundRect(ctx, x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
        }
        
        function downloadIcon(size) {
            const canvas = createIconCanvas(size);
            const link = document.createElement('a');
            link.download = `seqqo_icon_${size}x${size}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function downloadAllSizes() {
            const sizes = [16, 32, 48, 72, 96, 144, 192, 512, 1024];
            
            sizes.forEach((size, index) => {
                setTimeout(() => {
                    downloadIcon(size);
                }, index * 500); // Délai entre chaque téléchargement
            });
        }
        
        // Prévisualisation interactive
        document.getElementById('iconPreview').addEventListener('click', function() {
            downloadIcon(1024);
        });
    </script>
</body>
</html>
