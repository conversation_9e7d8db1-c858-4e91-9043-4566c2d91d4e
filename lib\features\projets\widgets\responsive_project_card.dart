import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/project_data.dart';
import '../../../core/widgets/mobile_components/swipe_action_card.dart';
import '../../../core/utils/platform_utils.dart';
import '../../../core/utils/responsive_utils.dart';
import 'package:url_launcher/url_launcher.dart';

/// Widget de carte de projet adaptatif pour les vues mobiles avec style Notion
class ResponsiveProjectCard extends StatelessWidget {
  final ProjectData project;
  final String? clientName;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;
  final VoidCallback? onEdit;
  final VoidCallback? onCallClient;
  final VoidCallback? onEmailClient;
  final bool isSelected;

  const ResponsiveProjectCard({
    super.key,
    required this.project,
    this.clientName,
    this.onTap,
    this.onDelete,
    this.onEdit,
    this.onCallClient,
    this.onEmailClient,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Définir les couleurs et icônes par statut
    IconData statusIcon;
    Color statusColor;
    String statusText;

    switch (project.status.toLowerCase()) {
      // Statuts standard
      case 'nouveau':
        statusIcon = Icons.fiber_new_outlined;
        statusColor = Colors.orange.shade600;
        statusText = 'Nouveau';
        break;
      case 'en_cours':
        statusIcon = Icons.pending_outlined;
        statusColor = Colors.green.shade600;
        statusText = 'En cours';
        break;
      case 'terminé':
      case 'termine': // Sans accent
        statusIcon = Icons.task_alt;
        statusColor = Colors.blue.shade600;
        statusText = 'Terminé';
        break;

      // Statuts MOE - Maîtrise d'œuvre
      case 'esquisse':
        statusIcon = Icons.draw;
        statusColor = Colors.indigo.shade300;
        statusText = 'Esquisse';
        break;
      case 'etude_audit':
        statusIcon = Icons.search;
        statusColor = Colors.blue.shade500;
        statusText = 'Étude / Audit';
        break;
      case 'aps':
        statusIcon = Icons.description_outlined;
        statusColor = Colors.teal.shade300;
        statusText = 'APS';
        break;
      case 'conception':
        statusIcon = Icons.architecture;
        statusColor = Colors.purple.shade500;
        statusText = 'Conception';
        break;
      case 'apd':
        statusIcon = Icons.article_outlined;
        statusColor = Colors.deepPurple.shade300;
        statusText = 'APD';
        break;
      case 'dossiers_admin':
        statusIcon = Icons.folder_special;
        statusColor = Colors.amber.shade700;
        statusText = 'Dossiers Admin';
        break;
      case 'dce':
        statusIcon = Icons.folder_copy_outlined;
        statusColor = Colors.amber.shade500;
        statusText = 'DCE';
        break;
      case 'analyse_offres':
        statusIcon = Icons.analytics_outlined;
        statusColor = Colors.lightBlue.shade700;
        statusText = 'Analyse Offres';
        break;
      case 'suivi_chantier':
        statusIcon = Icons.build_outlined;
        statusColor = Colors.green.shade700;
        statusText = 'Suivi Chantier';
        break;
      case 'execution':
        statusIcon = Icons.construction;
        statusColor = Colors.red.shade500;
        statusText = 'Exécution';
        break;
      case 'reception':
        statusIcon = Icons.check_circle_outline;
        statusColor = Colors.lightGreen.shade700;
        statusText = 'Réception';
        break;

      // Autres statuts
      case 'prepa_chantier':
        statusIcon = Icons.engineering;
        statusColor = Colors.orange.shade500;
        statusText = 'Prépa Chantier';
        break;
      case 'livraison':
        statusIcon = Icons.check_circle;
        statusColor = Colors.green.shade500;
        statusText = 'Livraison';
        break;
      case 'annule':
        statusIcon = Icons.cancel;
        statusColor = Colors.grey.shade600;
        statusText = 'Annulé';
        break;
      default:
        statusIcon = Icons.help_outline;
        statusColor = Colors.grey;
        statusText = project.status;
    }

    // Formater la date
    final String formattedDate = project.createdAt != null
        ? DateFormat('dd/MM/yyyy').format(project.createdAt!.toDate())
        : 'N/A';

    // Wrapper avec actions de swipe pour mobile
    Widget cardContent = Card(
      elevation: 0,
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12), // Seqqo border radius
        side: BorderSide(
          color: isSelected
              ? colorScheme.primary.withAlpha(100)
              : colorScheme.outline.withAlpha(40),
          width: 1,
        ),
      ),
      color:
          isSelected ? colorScheme.primary.withAlpha(15) : colorScheme.surface,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        hoverColor: colorScheme.primary.withAlpha(10),
        child: Padding(
          padding: const EdgeInsets.all(16.0), // Augmenté pour mobile
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Première ligne: Nom du projet et statut
              Row(
                children: [
                  Icon(
                    Icons.business,
                    size: 14,
                    color: isSelected
                        ? colorScheme.primary
                        : colorScheme.onSurface.withAlpha(150),
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      project.projectName ?? 'Sans nom',
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.w500,
                        color: isSelected
                            ? colorScheme.primary
                            : colorScheme.onSurface,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 6),
                  // Statut avec style Notion
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                    decoration: BoxDecoration(
                      color: statusColor.withAlpha(20),
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                          color: statusColor.withAlpha(60), width: 1),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          statusIcon,
                          size: 12,
                          color: statusColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          statusText,
                          style: TextStyle(
                            color: statusColor,
                            fontSize: 11,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Deuxième ligne: Client et numéro de projet
              Row(
                children: [
                  Icon(
                    Icons.person_outline,
                    size: 14,
                    color: colorScheme.onSurface.withAlpha(120),
                  ),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      clientName ?? 'Client inconnu',
                      style: TextStyle(
                        fontSize: 12,
                        color: colorScheme.onSurface.withAlpha(180),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (project.projectNumber != null &&
                      project.projectNumber!.isNotEmpty) ...[
                    const SizedBox(width: 8),
                    Icon(
                      Icons.tag,
                      size: 14,
                      color: colorScheme.onSurface.withAlpha(120),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      project.projectNumber!,
                      style: TextStyle(
                        fontSize: 12,
                        color: colorScheme.onSurface.withAlpha(180),
                      ),
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 8),

              // Troisième ligne: Adresse
              if (project.siteAddress != null || project.siteCity != null)
                Row(
                  children: [
                    Icon(
                      Icons.location_on_outlined,
                      size: 14,
                      color: colorScheme.onSurface.withAlpha(120),
                    ),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        [
                          project.siteAddress,
                          project.siteCity,
                        ].where((e) => e != null && e.isNotEmpty).join(', '),
                        style: TextStyle(
                          fontSize: 12,
                          color: colorScheme.onSurface.withAlpha(180),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),

              const SizedBox(height: 8),

              // Quatrième ligne: Date et indicateur de swipe
              Row(
                children: [
                  Icon(
                    Icons.calendar_today_outlined,
                    size: 14,
                    color: colorScheme.onSurface.withAlpha(120),
                  ),
                  const SizedBox(width: 6),
                  Text(
                    formattedDate,
                    style: TextStyle(
                      fontSize: 11,
                      color: colorScheme.onSurface.withAlpha(150),
                    ),
                  ),
                  const Spacer(),
                  // Indicateur de swipe sur mobile
                  Icon(
                    Icons.more_horiz,
                    size: 16,
                    color: colorScheme.onSurface.withAlpha(120),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );

    // Retourner avec actions de swipe sur mobile
    return SwipeActionCard(
      leftActions: [
        // Actions de communication
        if (onCallClient != null)
          SwipeAction.call(
            onPressed: onCallClient!,
          ),
        if (onEmailClient != null)
          SwipeAction.email(
            onPressed: onEmailClient!,
          ),
      ],
      rightActions: [
        // Actions de gestion
        if (onEdit != null)
          SwipeAction.edit(
            onPressed: onEdit!,
          ),
        if (onDelete != null)
          SwipeAction.delete(
            onPressed: onDelete!,
          ),
      ],
      child: cardContent,
    );
  }
}
