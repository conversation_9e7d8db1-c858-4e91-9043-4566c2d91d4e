import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../models/project_data.dart';
import '../providers/project_providers.dart';
import '../widgets/responsive_project_card.dart';
import '../../clients/services/client_storage_service.dart';
import '../../../core/utils/feedback_utils.dart';
import '../../../core/utils/platform_utils.dart';
import 'package:url_launcher/url_launcher.dart';

/// Widget pour afficher une liste de projets
class PaginatedProjectListWidget extends ConsumerStatefulWidget {
  final List<ProjectData>? projects; // Liste de projets à afficher
  final ValueChanged<ProjectData>? onProjectSelected;
  final ValueChanged<ProjectData>? onDeleteProject;
  final ValueChanged<ProjectData>? onOpenFullPage;
  final ProjectData? selectedProject;
  final Map<String, String>? clientNames;

  const PaginatedProjectListWidget({
    super.key,
    this.projects, // Optionnel - si null, utilisera le provider
    this.onProjectSelected,
    this.onDeleteProject,
    this.onOpenFullPage,
    this.selectedProject,
    this.clientNames,
  });

  @override
  ConsumerState<PaginatedProjectListWidget> createState() =>
      _PaginatedProjectListWidgetState();
}

class _PaginatedProjectListWidgetState
    extends ConsumerState<PaginatedProjectListWidget> {
  final ScrollController _scrollController = ScrollController();
  final ClientStorageService _clientService = ClientStorageService();
  Map<String, String> _clientNames = {};
  bool _isLoadingClients = true;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Si les noms de clients ne sont pas fournis, les charger localement.
    if (widget.clientNames == null) {
      _loadClientNames();
    } else {
      _clientNames = widget.clientNames!;
      _isLoadingClients = false;
    }

    // Charger la première page au démarrage seulement si aucune liste n'est fournie
    if (widget.projects == null) {
      Future.microtask(() {
        ref.read(projectPaginationProvider.notifier).loadFirstPage();
      });
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  // Chargement des noms de clients pour l'affichage
  Future<void> _loadClientNames() async {
    setState(() {
      _isLoadingClients = true;
    });

    try {
      final clients = await _clientService.listClients();
      final Map<String, String> names = {};
      for (final client in clients) {
        names[client.clientId] = client.name ?? 'Sans nom';
      }

      if (mounted) {
        setState(() {
          _clientNames = names;
          _isLoadingClients = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingClients = false;
        });
        showFeedbackSnackBar(
          context,
          message: 'Erreur lors du chargement des clients: $e',
          isError: true,
        );
      }
    }
  }

  // Détection du défilement pour charger plus de projets
  void _onScroll() {
    // Ne pas charger plus de projets si une liste est fournie
    if (widget.projects != null) return;

    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final state = ref.read(projectPaginationProvider);
      if (state.hasMore && !state.isLoading) {
        ref.read(projectPaginationProvider.notifier).loadNextPage();
      }
    }
  }

  // Confirmation de suppression
  Future<void> _confirmDelete(BuildContext context, ProjectData project) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmation'),
        content: Text(
            'Voulez-vous vraiment supprimer le projet "${project.projectName ?? 'Sans nom'}" ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );

    if (confirmed == true && widget.onDeleteProject != null) {
      widget.onDeleteProject!(project);
    }
  }

  // Construction d'un indicateur de statut style Notion
  Widget _buildStatusIndicator(BuildContext context, ProjectData project) {
    try {
      // Définir les couleurs et icônes par statut
      IconData statusIcon;
      Color statusColor;
      String statusText;
      String statusDescription;

      // Vérifier si le statut est null ou vide
      String status = project.status;
      if (status.isEmpty) {
        // Valeur par défaut si le statut est vide
        statusIcon = Icons.fiber_new_outlined;
        statusColor = Colors.orange.shade600;
        statusText = 'Nouveau';
        statusDescription = 'Nouveau projet à démarrer';
      } else {
        // Traiter le statut normalement
        switch (status.toLowerCase()) {
          // Statuts standard
          case 'nouveau':
            statusIcon = Icons.fiber_new_outlined;
            statusColor = Colors.orange.shade600;
            statusText = 'Nouveau';
            statusDescription = 'Nouveau projet à démarrer';
            break;
          case 'en_cours':
            statusIcon = Icons.pending_outlined;
            statusColor = Colors.green.shade600;
            statusText = 'En cours';
            statusDescription = 'Projet en cours de réalisation';
            break;
          case 'terminé':
          case 'termine': // Sans accent
            statusIcon = Icons.task_alt;
            statusColor = Colors.blue.shade600;
            statusText = 'Terminé';
            statusDescription = 'Projet terminé';
            break;

          // Statuts MOE - Maîtrise d'œuvre
          case 'esquisse':
            statusIcon = Icons.draw;
            statusColor = Colors.indigo.shade300;
            statusText = 'Esquisse';
            statusDescription = 'Phase d\'esquisse (15%)';
            break;
          case 'etude_audit':
            statusIcon = Icons.search;
            statusColor = Colors.blue.shade500;
            statusText = 'Étude / Audit';
            statusDescription = 'Phase d\'étude ou d\'audit (20%)';
            break;
          case 'aps':
            statusIcon = Icons.description_outlined;
            statusColor = Colors.teal.shade300;
            statusText = 'Avant-Projet Sommaire';
            statusDescription = 'Phase d\'avant-projet sommaire (25%)';
            break;
          case 'conception':
            statusIcon = Icons.architecture;
            statusColor = Colors.purple.shade500;
            statusText = 'Conception';
            statusDescription = 'Phase de conception (30%)';
            break;
          case 'apd':
            statusIcon = Icons.article_outlined;
            statusColor = Colors.deepPurple.shade300;
            statusText = 'Avant-Projet Définitif';
            statusDescription = 'Phase d\'avant-projet définitif (35%)';
            break;
          case 'dossiers_admin':
            statusIcon = Icons.folder_special;
            statusColor = Colors.amber.shade700;
            statusText = 'Dossiers Admin';
            statusDescription = 'Phase de dossiers administratifs (40%)';
            break;
          case 'dce':
            statusIcon = Icons.folder_copy_outlined;
            statusColor = Colors.amber.shade500;
            statusText = 'Dossier de Consultation';
            statusDescription =
                'Phase de dossier de consultation des entreprises (45%)';
            break;
          case 'analyse_offres':
            statusIcon = Icons.analytics_outlined;
            statusColor = Colors.lightBlue.shade700;
            statusText = 'Analyse des Offres';
            statusDescription = 'Phase d\'analyse des offres (55%)';
            break;
          case 'suivi_chantier':
            statusIcon = Icons.build_outlined;
            statusColor = Colors.green.shade700;
            statusText = 'Suivi de Chantier';
            statusDescription = 'Phase de suivi de chantier (70%)';
            break;
          case 'execution':
            statusIcon = Icons.construction;
            statusColor = Colors.red.shade500;
            statusText = 'Exécution';
            statusDescription = 'Phase d\'exécution des travaux (80%)';
            break;
          case 'reception':
            statusIcon = Icons.check_circle_outline;
            statusColor = Colors.lightGreen.shade700;
            statusText = 'Réception';
            statusDescription = 'Phase de réception des travaux (90%)';
            break;

          // Autres statuts
          case 'prepa_chantier':
            statusIcon = Icons.engineering;
            statusColor = Colors.orange.shade500;
            statusText = 'Prépa Chantier';
            statusDescription = 'Préparation du chantier';
            break;
          case 'livraison':
            statusIcon = Icons.check_circle;
            statusColor = Colors.green.shade500;
            statusText = 'Livraison';
            statusDescription = 'Livraison du projet';
            break;
          case 'annule':
            statusIcon = Icons.cancel;
            statusColor = Colors.grey.shade600;
            statusText = 'Annulé';
            statusDescription = 'Projet annulé';
            break;
          default:
            statusIcon = Icons.help_outline;
            statusColor = Colors.grey;
            statusText = status;
            statusDescription = 'Statut: $status';
        }
      }

      return Tooltip(
        message: statusDescription,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: statusColor.withAlpha(20),
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: statusColor.withAlpha(60), width: 1),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                statusIcon,
                size: 14,
                color: statusColor,
              ),
              const SizedBox(width: 6),
              Flexible(
                child: Text(
                  statusText,
                  style: TextStyle(
                    color: statusColor,
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      );
    } catch (e) {
      // En cas d'erreur, afficher un indicateur de statut par défaut
      debugPrint('Erreur dans _buildStatusIndicator: $e');
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.grey.withAlpha(20),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.grey.withAlpha(60), width: 1),
        ),
        child: const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: 14,
              color: Colors.grey,
            ),
            SizedBox(width: 6),
            Flexible(
              child: Text(
                'Inconnu',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      );
    }
  }

  // Formatage de date
  String _formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy').format(date);
  }

  // Construit la vue mobile avec des cartes
  Widget _buildMobileView(BuildContext context, List<ProjectData> projects) {
    return ListView.builder(
      controller: _scrollController,
      itemCount: projects.length,
      itemBuilder: (context, index) {
        final project = projects[index];
        final clientName =
            (widget.clientNames ?? _clientNames)[project.clientId] ??
                'Client inconnu';
        return ResponsiveProjectCard(
          project: project,
          clientName: clientName,
          isSelected: widget.selectedProject?.projectId == project.projectId,
          onTap: () {
            // Vérifier que l'ID du projet n'est pas vide avant de le sélectionner
            if (project.projectId.isNotEmpty) {
              widget.onProjectSelected?.call(project);
            } else {
              debugPrint(
                  'Warning: Attempted to select project with empty ID in mobile view');
              showFeedbackSnackBar(
                context,
                message: 'Erreur: Projet invalide',
                isError: true,
              );
            }
          },
          onDelete: () => _confirmDelete(context, project),
          onEdit: () {
            if (widget.onOpenFullPage != null) {
              widget.onOpenFullPage!(project);
            }
          },
          onCallClient: () => _callClient(project),
          onEmailClient: () => _emailClient(project),
        );
      },
    );
  }

  // Construit la vue bureau avec un tableau style Notion
  Widget _buildDesktopView(
      BuildContext context, List<ProjectData> projects, ThemeData theme) {
    return Column(
      children: [
        // En-tête de tableau fixe
        Material(
          elevation: 1,
          shadowColor: theme.colorScheme.shadow.withAlpha(50),
          child: Container(
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: theme.colorScheme.outline.withAlpha(60),
                  width: 1,
                ),
              ),
              color: theme.colorScheme.surface,
            ),
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
            child: Row(
              children: [
                // Nom
                Expanded(
                  flex: 3,
                  child: _buildColumnHeader(
                    'Nom',
                    Icons.business,
                    theme,
                    onSort: () {
                      ref
                          .read(projectPaginationProvider.notifier)
                          .changeSorting(
                            'projectName',
                            !ref.read(projectPaginationProvider).sortDirection,
                          );
                    },
                    isSorted: ref.read(projectPaginationProvider).sortField ==
                        'projectName',
                    sortDirection:
                        ref.read(projectPaginationProvider).sortDirection,
                    tooltip: 'Trier par nom de projet',
                  ),
                ),
                _buildVerticalDivider(theme),
                // Client
                Expanded(
                  flex: 2,
                  child: _buildColumnHeader(
                    'Client',
                    Icons.person_outline,
                    theme,
                    onSort: () {
                      ref
                          .read(projectPaginationProvider.notifier)
                          .changeSorting(
                            'clientId',
                            !ref.read(projectPaginationProvider).sortDirection,
                          );
                    },
                    isSorted: ref.read(projectPaginationProvider).sortField ==
                        'clientId',
                    sortDirection:
                        ref.read(projectPaginationProvider).sortDirection,
                    tooltip: 'Trier par nom de client',
                  ),
                ),
                _buildVerticalDivider(theme),
                // Numéro
                Expanded(
                  flex: 1,
                  child: _buildColumnHeader(
                    'Numéro',
                    Icons.tag,
                    theme,
                    tooltip: 'Numéro de référence du projet',
                  ),
                ),
                _buildVerticalDivider(theme),
                // Adresse
                Expanded(
                  flex: 2,
                  child: _buildColumnHeader(
                    'Adresse',
                    Icons.location_on_outlined,
                    theme,
                    tooltip: 'Adresse du site',
                  ),
                ),
                _buildVerticalDivider(theme),
                // Ville
                Expanded(
                  flex: 1,
                  child: _buildColumnHeader(
                    'Ville',
                    Icons.location_city_outlined,
                    theme,
                    tooltip: 'Ville du site',
                  ),
                ),
                _buildVerticalDivider(theme),
                // Statut
                Expanded(
                  flex: 1,
                  child: _buildColumnHeader(
                    'Statut',
                    Icons.flag_outlined,
                    theme,
                    onSort: () {
                      ref
                          .read(projectPaginationProvider.notifier)
                          .changeSorting(
                            'status',
                            !ref.read(projectPaginationProvider).sortDirection,
                          );
                    },
                    isSorted: ref.read(projectPaginationProvider).sortField ==
                        'status',
                    sortDirection:
                        ref.read(projectPaginationProvider).sortDirection,
                    tooltip: 'Trier par statut du projet',
                  ),
                ),
                _buildVerticalDivider(theme),
                // Date
                Expanded(
                  flex: 1,
                  child: _buildColumnHeader(
                    'Date',
                    Icons.calendar_today_outlined,
                    theme,
                    onSort: () {
                      ref
                          .read(projectPaginationProvider.notifier)
                          .changeSorting(
                            'createdAt',
                            !ref.read(projectPaginationProvider).sortDirection,
                          );
                    },
                    isSorted: ref.read(projectPaginationProvider).sortField ==
                        'createdAt',
                    sortDirection:
                        ref.read(projectPaginationProvider).sortDirection,
                    tooltip: 'Trier par date de création',
                  ),
                ),
                _buildVerticalDivider(theme),
                // Type
                Expanded(
                  flex: 1,
                  child: _buildColumnHeader(
                    'Type',
                    Icons.category_outlined,
                    theme,
                    tooltip: 'Type d\'enseigne',
                  ),
                ),
                _buildVerticalDivider(theme),
                // Actions (largeur fixe)
                SizedBox(
                  width: 100,
                  child: _buildColumnHeader(
                    'Actions',
                    Icons.more_horiz,
                    theme,
                    showSortIcon: false,
                    tooltip: 'Actions disponibles',
                  ),
                ),
              ],
            ),
          ),
        ),

        // Corps du tableau avec défilement
        Expanded(
          child: SingleChildScrollView(
            controller: _scrollController,
            child: Column(
              children: projects.map((project) {
                final isSelected =
                    widget.selectedProject?.projectId == project.projectId;

                return _buildProjectRow(
                  context,
                  project,
                  isSelected,
                  theme,
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  // Construit un séparateur vertical pour le tableau
  Widget _buildVerticalDivider(ThemeData theme) {
    return Container(
      width: 1,
      height: 20,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      color: theme.colorScheme.outline.withAlpha(30),
    );
  }

  // Construit un en-tête de colonne
  Widget _buildColumnHeader(
    String title,
    IconData icon,
    ThemeData theme, {
    VoidCallback? onSort,
    bool isSorted = false,
    bool sortDirection = true,
    bool showSortIcon = true,
    String? tooltip,
  }) {
    return Tooltip(
      message: tooltip ?? title,
      waitDuration: const Duration(milliseconds: 500),
      child: InkWell(
        onTap: onSort,
        borderRadius: BorderRadius.circular(4),
        hoverColor: theme.colorScheme.primary.withAlpha(15),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 18,
                color: isSorted
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface.withAlpha(150),
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                    color: isSorted
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface.withAlpha(200),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (showSortIcon && onSort != null) ...[
                const SizedBox(width: 4),
                Icon(
                  isSorted
                      ? (sortDirection
                          ? Icons.arrow_upward
                          : Icons.arrow_downward)
                      : Icons.unfold_more,
                  size: 16,
                  color: isSorted
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurface.withAlpha(100),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // Construit une ligne de projet
  Widget _buildProjectRow(
    BuildContext context,
    ProjectData project,
    bool isSelected,
    ThemeData theme,
  ) {
    final clientName = (widget.clientNames ?? _clientNames)[project.clientId] ??
        'Client inconnu';
    return Material(
      color: isSelected
          ? theme.colorScheme.primary.withAlpha(15)
          : theme.colorScheme.surface,
      child: InkWell(
        onTap: () {
          // Vérifier que l'ID du projet n'est pas vide avant de le sélectionner
          if (project.projectId.isNotEmpty) {
            widget.onProjectSelected?.call(project);
          } else {
            debugPrint('Warning: Attempted to select project with empty ID');
            showFeedbackSnackBar(
              context,
              message: 'Erreur: Projet invalide',
              isError: true,
            );
          }
        },
        hoverColor: theme.colorScheme.primary.withAlpha(10),
        child: Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: theme.colorScheme.outline.withAlpha(30),
                width: 1,
              ),
            ),
          ),
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
          child: Row(
            children: [
              // Nom
              Expanded(
                flex: 3,
                child: Row(
                  children: [
                    Icon(
                      Icons.business,
                      size: 18,
                      color: isSelected
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurface.withAlpha(150),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Tooltip(
                        message: project.projectName ?? 'Sans nom',
                        child: Text(
                          project.projectName ?? 'Sans nom',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight:
                                isSelected ? FontWeight.w600 : FontWeight.w500,
                            color: isSelected
                                ? theme.colorScheme.primary
                                : theme.colorScheme.onSurface,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              _buildVerticalDivider(theme),

              // Client
              Expanded(
                flex: 2,
                child: Tooltip(
                  message: clientName,
                  child: Text(
                    clientName,
                    style: TextStyle(
                      fontSize: 14,
                      color: theme.colorScheme.onSurface,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),

              _buildVerticalDivider(theme),

              // Numéro
              Expanded(
                flex: 1,
                child: Tooltip(
                  message: project.projectNumber ?? 'Non défini',
                  child: Text(
                    project.projectNumber ?? 'N/A',
                    style: TextStyle(
                      fontSize: 14,
                      color: theme.colorScheme.onSurface,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),

              _buildVerticalDivider(theme),

              // Adresse
              Expanded(
                flex: 2,
                child: Tooltip(
                  message: project.siteAddress ?? 'Adresse non définie',
                  child: Text(
                    project.siteAddress ?? 'Non définie',
                    style: TextStyle(
                      fontSize: 14,
                      color: theme.colorScheme.onSurface,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),

              _buildVerticalDivider(theme),

              // Ville
              Expanded(
                flex: 1,
                child: Tooltip(
                  message: project.siteCity ?? 'Ville non définie',
                  child: Text(
                    project.siteCity ?? 'Non définie',
                    style: TextStyle(
                      fontSize: 14,
                      color: theme.colorScheme.onSurface,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),

              _buildVerticalDivider(theme),

              // Statut
              Expanded(
                flex: 1,
                child: _buildStatusIndicator(context, project),
              ),

              _buildVerticalDivider(theme),

              // Date
              Expanded(
                flex: 1,
                child: Tooltip(
                  message: project.createdAt != null
                      ? 'Créé le ${_formatDate(project.createdAt!.toDate())}'
                      : 'Date non définie',
                  child: Text(
                    project.createdAt != null
                        ? _formatDate(project.createdAt!.toDate())
                        : 'N/A',
                    style: TextStyle(
                      fontSize: 13,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ),

              _buildVerticalDivider(theme),

              // Type
              Expanded(
                flex: 1,
                child: buildEnseigneTypeChip(
                  project.enseigneType,
                  iconSize: 16,
                  spacing: 4,
                ),
              ),

              _buildVerticalDivider(theme),

              // Actions (largeur fixe)
              SizedBox(
                width: 100,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Material(
                      color: Colors.transparent,
                      child: Tooltip(
                        message: 'Ouvrir le tableau de bord',
                        child: InkWell(
                          onTap: () => widget.onOpenFullPage?.call(project),
                          borderRadius: BorderRadius.circular(20),
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Icon(
                              Icons.dashboard_outlined,
                              color: theme.colorScheme.primary,
                              size: 20,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 4),
                    Material(
                      color: Colors.transparent,
                      child: Tooltip(
                        message: 'Supprimer le projet',
                        child: InkWell(
                          onTap: () => _confirmDelete(context, project),
                          borderRadius: BorderRadius.circular(20),
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Icon(
                              Icons.delete_outline,
                              color: theme.colorScheme.error,
                              size: 20,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Construction d'un chip pour le type d'enseigne style Notion
  Widget buildEnseigneTypeChip(String? enseigneType,
      {double iconSize = 16, double spacing = 4}) {
    try {
      if (enseigneType == null || enseigneType.isEmpty) {
        return const SizedBox.shrink();
      }

      IconData icon;
      Color color;
      String label;
      String description;

      switch (enseigneType.toLowerCase()) {
        case 'lumineuse':
          icon = Icons.lightbulb_outline;
          color = Colors.amber.shade600;
          label = 'Lumineuse';
          description = 'Enseigne avec éclairage intégré';
          break;
        case 'non lumineuse':
          icon = Icons.lightbulb_outline;
          color = Colors.blueGrey.shade400;
          label = 'Non lumineuse';
          description = 'Enseigne sans éclairage intégré';
          break;
        default:
          icon = Icons.help_outline;
          color = Colors.grey;
          label = enseigneType;
          description = 'Type d\'enseigne: $enseigneType';
      }

      return Tooltip(
        message: description,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: color.withAlpha(20),
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: color.withAlpha(60), width: 1),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: iconSize, color: color),
              SizedBox(width: spacing),
              Flexible(
                child: Text(
                  label,
                  style: TextStyle(
                    color: color,
                    fontSize: 13,
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      );
    } catch (e) {
      // En cas d'erreur, afficher un chip par défaut
      debugPrint('Erreur dans buildEnseigneTypeChip: $e');
      return const SizedBox.shrink();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isMobile = MediaQuery.of(context).size.width < 800;

    // Utiliser soit les projets fournis directement, soit ceux du provider
    List<ProjectData> projectsToDisplay;
    bool isLoading = false;

    if (widget.projects != null) {
      // Utiliser les projets fournis directement
      projectsToDisplay = widget.projects!;
    } else {
      // Utiliser les projets du provider (comportement original)
      final state = ref.watch(projectPaginationProvider);
      projectsToDisplay = state.projects;
      isLoading = state.isLoading;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Liste des projets
        Expanded(
          child: _isLoadingClients && projectsToDisplay.isEmpty
              ? Center(
                  child: CircularProgressIndicator(
                    color: theme.colorScheme.primary,
                    strokeWidth: 2,
                  ),
                )
              : projectsToDisplay.isEmpty && !isLoading
                  ? Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.folder_off_outlined,
                            size: 48,
                            color: theme.colorScheme.onSurface.withAlpha(100),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Aucun projet trouvé',
                            style: TextStyle(
                              fontSize: 16,
                              color: theme.colorScheme.onSurface.withAlpha(150),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Essayez de modifier vos filtres ou créez un nouveau projet',
                            style: TextStyle(
                              fontSize: 13,
                              color: theme.colorScheme.onSurface.withAlpha(120),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    )
                  : isMobile
                      ? _buildMobileView(context, projectsToDisplay)
                      : _buildDesktopView(context, projectsToDisplay, theme),
        ),

        // Indicateur de chargement en bas de liste
        if (isLoading &&
            widget.projects ==
                null) // Afficher uniquement si on utilise le provider
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  color: theme.colorScheme.primary,
                  strokeWidth: 2,
                ),
              ),
            ),
          ),
      ],
    );
  }

  // Actions de swipe pour les projets
  void _callClient(ProjectData project) {
    // TODO: Implémenter l'appel du client basé sur les données du projet
    // Pour l'instant, on peut utiliser un numéro générique ou chercher dans les données client
    PlatformUtils.triggerHapticFeedback(HapticFeedbackType.medium);

    // Exemple d'implémentation - à adapter selon la structure des données
    showFeedbackSnackBar(
      context,
      message: 'Appel du client pour le projet ${project.projectName}',
      isError: false,
    );
  }

  void _emailClient(ProjectData project) {
    // TODO: Implémenter l'envoi d'email au client basé sur les données du projet
    PlatformUtils.triggerHapticFeedback(HapticFeedbackType.medium);

    // Exemple d'implémentation - à adapter selon la structure des données
    showFeedbackSnackBar(
      context,
      message: 'Email au client pour le projet ${project.projectName}',
      isError: false,
    );
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      }
    } catch (e) {
      debugPrint('Erreur lors du lancement de l\'URL: $e');
    }
  }
}
