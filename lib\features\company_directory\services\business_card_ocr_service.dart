import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logging/logging.dart';

/// Modèle pour les données extraites d'une carte de visite
class BusinessCardData {
  final String? companyName;
  final String? contactName;
  final String? email;
  final String? phoneNumber;
  final String? address;
  final String? website;
  final String? jobTitle;
  final List<String> categories;

  const BusinessCardData({
    this.companyName,
    this.contactName,
    this.email,
    this.phoneNumber,
    this.address,
    this.website,
    this.jobTitle,
    this.categories = const [],
  });

  @override
  String toString() {
    return 'BusinessCardData(company: $companyName, contact: $contactName, email: $email, phone: $phoneNumber)';
  }
}

/// Service OCR pour extraire les informations des cartes de visite
class BusinessCardOCRService {
  static final Logger _logger = Logger('BusinessCardOCRService');
  static final ImagePicker _picker = ImagePicker();
  static final TextRecognizer _textRecognizer = TextRecognizer();

  /// Scanner une carte de visite depuis la caméra
  static Future<BusinessCardData?> scanFromCamera() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
        preferredCameraDevice: CameraDevice.rear,
      );

      if (image == null) {
        _logger.info('Scan annulé par l\'utilisateur');
        return null;
      }

      return await _processImage(image);
    } catch (e, stackTrace) {
      _logger.severe('Erreur lors du scan depuis la caméra', e, stackTrace);
      rethrow;
    }
  }

  /// Scanner une carte de visite depuis la galerie
  static Future<BusinessCardData?> scanFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image == null) {
        _logger.info('Sélection annulée par l\'utilisateur');
        return null;
      }

      return await _processImage(image);
    } catch (e, stackTrace) {
      _logger.severe('Erreur lors du scan depuis la galerie', e, stackTrace);
      rethrow;
    }
  }

  /// Traiter une image et extraire les données
  static Future<BusinessCardData?> _processImage(XFile image) async {
    try {
      _logger.info('Traitement de l\'image: ${image.path}');

      // Créer l'objet InputImage pour ML Kit
      final inputImage = InputImage.fromFilePath(image.path);

      // Reconnaissance de texte
      final RecognizedText recognizedText = await _textRecognizer.processImage(inputImage);

      if (recognizedText.text.isEmpty) {
        _logger.warning('Aucun texte détecté dans l\'image');
        return null;
      }

      _logger.info('Texte détecté: ${recognizedText.text}');

      // Parser le texte extrait
      return _parseBusinessCardText(recognizedText.text);
    } catch (e, stackTrace) {
      _logger.severe('Erreur lors du traitement de l\'image', e, stackTrace);
      rethrow;
    }
  }

  /// Parser le texte extrait pour identifier les informations
  static BusinessCardData _parseBusinessCardText(String text) {
    final lines = text.split('\n').map((line) => line.trim()).where((line) => line.isNotEmpty).toList();
    
    String? companyName;
    String? contactName;
    String? email;
    String? phoneNumber;
    String? address;
    String? website;
    String? jobTitle;
    List<String> categories = [];

    // Expressions régulières pour identifier les patterns
    final emailRegex = RegExp(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b');
    final phoneRegex = RegExp(r'(\+33|0)[1-9](?:[0-9]{8})|\+33\s?[1-9](?:\s?[0-9]{2}){4}|0[1-9](?:\s?[0-9]{2}){4}');
    final websiteRegex = RegExp(r'(https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)');

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      final lowerLine = line.toLowerCase();

      // Email
      if (email == null && emailRegex.hasMatch(line)) {
        email = emailRegex.firstMatch(line)?.group(0);
        _logger.info('Email détecté: $email');
        continue;
      }

      // Téléphone
      if (phoneNumber == null && phoneRegex.hasMatch(line)) {
        phoneNumber = phoneRegex.firstMatch(line)?.group(0);
        _logger.info('Téléphone détecté: $phoneNumber');
        continue;
      }

      // Site web
      if (website == null && websiteRegex.hasMatch(line)) {
        website = websiteRegex.firstMatch(line)?.group(0);
        if (!website!.startsWith('http')) {
          website = 'https://$website';
        }
        _logger.info('Site web détecté: $website');
        continue;
      }

      // Mots-clés pour identifier le type d'entreprise
      if (_isCompanyIndicator(lowerLine)) {
        companyName ??= line;
        categories.addAll(_extractCategories(lowerLine));
        _logger.info('Nom d\'entreprise détecté: $companyName');
        continue;
      }

      // Titre de poste
      if (_isJobTitle(lowerLine)) {
        jobTitle ??= line;
        _logger.info('Titre de poste détecté: $jobTitle');
        continue;
      }

      // Adresse (contient des mots-clés d'adresse)
      if (address == null && _isAddress(lowerLine)) {
        address = line;
        _logger.info('Adresse détectée: $address');
        continue;
      }

      // Nom de contact (généralement en première ligne si pas encore identifié)
      if (contactName == null && i < 3 && _isPersonName(line)) {
        contactName = line;
        _logger.info('Nom de contact détecté: $contactName');
      }
    }

    // Si pas de nom d'entreprise, prendre la première ligne non-identifiée
    companyName ??= lines.isNotEmpty ? lines.first : null;

    return BusinessCardData(
      companyName: companyName,
      contactName: contactName,
      email: email,
      phoneNumber: phoneNumber,
      address: address,
      website: website,
      jobTitle: jobTitle,
      categories: categories.toSet().toList(), // Supprimer les doublons
    );
  }

  /// Vérifier si une ligne indique un nom d'entreprise
  static bool _isCompanyIndicator(String line) {
    final indicators = [
      'sarl', 'sas', 'sa', 'eurl', 'sasu', 'snc', 'sci',
      'restaurant', 'café', 'bar', 'boulangerie', 'pharmacie',
      'garage', 'coiffeur', 'institut', 'cabinet', 'clinique',
      'entreprise', 'société', 'compagnie', 'group', 'holding'
    ];
    return indicators.any((indicator) => line.contains(indicator));
  }

  /// Extraire les catégories d'activité
  static List<String> _extractCategories(String line) {
    final categories = <String>[];
    final categoryMap = {
      'restaurant': 'Restaurant',
      'café': 'Restaurant',
      'bar': 'Restaurant',
      'boulangerie': 'Commerce',
      'pharmacie': 'Commerce',
      'garage': 'Service',
      'coiffeur': 'Service',
      'institut': 'Service',
      'cabinet': 'Service',
      'clinique': 'Service',
    };

    categoryMap.forEach((key, value) {
      if (line.contains(key)) {
        categories.add(value);
      }
    });

    return categories;
  }

  /// Vérifier si une ligne est un titre de poste
  static bool _isJobTitle(String line) {
    final titles = [
      'directeur', 'manager', 'chef', 'responsable', 'gérant',
      'président', 'pdg', 'dg', 'commercial', 'vendeur',
      'technicien', 'ingénieur', 'consultant', 'expert'
    ];
    return titles.any((title) => line.contains(title));
  }

  /// Vérifier si une ligne est une adresse
  static bool _isAddress(String line) {
    final addressKeywords = [
      'rue', 'avenue', 'boulevard', 'place', 'chemin', 'route',
      'bis', 'ter', 'cedex', 'bp', 'cs'
    ];
    return addressKeywords.any((keyword) => line.contains(keyword)) ||
           RegExp(r'\d{5}').hasMatch(line); // Code postal
  }

  /// Vérifier si une ligne est un nom de personne
  static bool _isPersonName(String line) {
    // Heuristique simple: 2-3 mots avec première lettre majuscule
    final words = line.split(' ');
    return words.length >= 2 && 
           words.length <= 3 && 
           words.every((word) => word.isNotEmpty && word[0].toUpperCase() == word[0]);
  }

  /// Nettoyer les ressources
  static Future<void> dispose() async {
    await _textRecognizer.close();
  }
}
