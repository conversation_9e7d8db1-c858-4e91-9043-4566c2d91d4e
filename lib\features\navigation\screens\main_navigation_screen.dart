// ignore_for_file: deprecated_member_use

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:logging/logging.dart';
import '../../../core/utils/feedback_utils.dart';
import '../../../features/organizations/widgets/organization_selector.dart';
import '../../../theme/color_scheme_extension.dart';
import '../../../core/widgets/seqqo_logo.dart';
import '../../../core/widgets/seqqo_favicon.dart';
import '../../../core/widgets/app_footer.dart';
import '../../../core/utils/platform_utils.dart';
import '../../../theme/mobile_theme_extensions.dart';
import '../providers/sidebar_provider.dart';
import '../widgets/sidebar_nav_item.dart';
import '../widgets/sidebar_section.dart';
import '../widgets/sidebar_toggle_button.dart';

// Déplacer _routeMap en dehors de la classe pour qu'il soit accessible globalement dans le fichier
const _routeMap = <String, int>{
  '/dashboard': 0,
  '/projects': 1,
  '/clients': 2,
  '/company-directory': 3,
  '/settings': 4,
  '/theme': 5,
};

class MainNavigationScreen extends ConsumerStatefulWidget {
  final Widget child;
  const MainNavigationScreen({super.key, required this.child});

  @override
  ConsumerState<MainNavigationScreen> createState() =>
      _MainNavigationScreenState();
}

class _MainNavigationScreenState extends ConsumerState<MainNavigationScreen> {
  User? _currentUser;
  StreamSubscription? _authStateSubscription;
  bool _isNavigating =
      false; // État de navigation pour l'indicateur de chargement

  // FocusNode pour gérer les raccourcis clavier
  final FocusNode _keyboardFocusNode = FocusNode();

  // Destinations de navigation avec des icônes plus grandes et des labels courts
  static const List<NavigationDestination> _destinations = [
    NavigationDestination(
      icon: Icon(Icons.dashboard_outlined, size: 24),
      selectedIcon: Icon(Icons.dashboard, size: 24),
      label: 'Accueil',
    ),
    NavigationDestination(
      icon: Icon(Icons.folder_copy_outlined, size: 24),
      selectedIcon: Icon(Icons.folder_copy, size: 24),
      label: 'Projets',
    ),
    NavigationDestination(
      icon: Icon(Icons.people_outline, size: 24),
      selectedIcon: Icon(Icons.people, size: 24),
      label: 'Clients',
    ),
    NavigationDestination(
      icon: Icon(Icons.business_outlined, size: 24),
      selectedIcon: Icon(Icons.business, size: 24),
      label: 'Annuaire',
    ),
    NavigationDestination(
      icon: Icon(Icons.settings_outlined, size: 24),
      selectedIcon: Icon(Icons.settings, size: 24),
      label: 'Réglages',
    ),
    NavigationDestination(
      icon: Icon(Icons.palette_outlined, size: 24),
      selectedIcon: Icon(Icons.palette, size: 24),
      label: 'Thème UI',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _authStateSubscription = FirebaseAuth.instance.authStateChanges().listen((
      User? user,
    ) {
      if (mounted) {
        setState(() {
          _currentUser = user;
        });
      }
    });
    _currentUser = FirebaseAuth.instance.currentUser;

    // Demander le focus pour pouvoir capturer les raccourcis clavier
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _keyboardFocusNode.requestFocus();
    });
  }

  void _onDestinationSelected(int index) async {
    // Éviter les navigations multiples simultanées
    if (_isNavigating) return;

    setState(() {
      _isNavigating = true;
    });

    // Utiliser le feedback haptique adaptatif
    PlatformUtils.triggerHapticFeedback(HapticFeedbackType.medium);
    final newRoute = _routeMap.entries.firstWhere((e) => e.value == index).key;

    // Ajouter un délai minimal pour une transition fluide
    await Future.delayed(PlatformUtils.getAnimationDuration());

    if (mounted) {
      context.go(newRoute);

      // Réinitialiser l'état après un court délai
      Future.delayed(PlatformUtils.getAnimationDuration(isLong: true), () {
        if (mounted) {
          setState(() {
            _isNavigating = false;
          });
        }
      });
    }
  }

  @override
  void dispose() {
    _authStateSubscription?.cancel();
    _keyboardFocusNode.dispose();
    super.dispose();
  }

  Future<void> _signOut() async {
    final logger = Logger('MainNavigation');
    try {
      await GoogleSignIn().signOut();
      await FirebaseAuth.instance.signOut();
      if (mounted) showFeedbackSnackBar(context, message: "Déconnecté.");
    } catch (e) {
      logger.warning("Erreur Sign Out: $e");
      if (mounted) {
        showFeedbackSnackBar(
          context,
          message: "Erreur déconnexion: $e",
          isError: true,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Appliquer les optimisations mobiles au thème si nécessaire
    final theme = PlatformUtils.shouldUseMobileOptimizations(context)
        ? MobileThemeExtensions.applyMobileOptimizations(
            context, Theme.of(context))
        : Theme.of(context);

    final isMobile = PlatformUtils.shouldUseMobileOptimizations(context);

    final location = GoRouterState.of(context).uri.toString();
    final selectedIndex = _routeMap.entries
        .firstWhere((e) => location.startsWith(e.key),
            orElse: () => _routeMap.entries.first)
        .value;
    final sidebarState = ref.watch(sidebarProvider);

    // Fonction de navigation optimisée
    void navigateTo(int index) async {
      // Éviter les navigations multiples simultanées
      if (_isNavigating) return;

      setState(() {
        _isNavigating = true;
      });

      HapticFeedback
          .lightImpact(); // Feedback tactile plus léger pour la sidebar
      final newRoute =
          _routeMap.entries.firstWhere((e) => e.value == index).key;

      // Transition fluide
      await Future.delayed(const Duration(milliseconds: 30));

      if (mounted) {
        context.go(newRoute);

        // Réinitialiser l'état
        Future.delayed(const Duration(milliseconds: 200), () {
          if (mounted) {
            setState(() {
              _isNavigating = false;
            });
          }
        });
      }
    }

    // Raccourci clavier pour basculer l'état du menu (Ctrl+B)
    return Focus(
        focusNode: _keyboardFocusNode,
        autofocus: true,
        onKeyEvent: (_, event) {
          // Vérifier si Ctrl+B est pressé
          if (event is KeyDownEvent &&
              event.logicalKey == LogicalKeyboardKey.keyB &&
              (HardwareKeyboard.instance.isControlPressed ||
                  HardwareKeyboard.instance.isMetaPressed)) {
            ref.read(sidebarProvider.notifier).handleKeyboardShortcut();
            return KeyEventResult.handled;
          }
          return KeyEventResult.ignored;
        },
        child: Scaffold(
          body: isMobile
              ? widget.child
              : Row(
                  children: [
                    const _AppSidebar(),
                    Expanded(
                      child: Column(
                        children: [
                          Expanded(child: widget.child),
                          const AppFooter(),
                        ],
                      ),
                    ),
                  ],
                ),
          backgroundColor: theme.colorScheme.surface,
          // Barre de navigation pour les écrans mobiles
          bottomNavigationBar: isMobile
              ? Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Footer global pour mobile (version compacte)
                    const AppFooter(isCompact: true),

                    // Barre de navigation
                    Container(
                      constraints: const BoxConstraints(
                        maxHeight: 90, // Augmenter encore la hauteur
                      ),
                      decoration: const BoxDecoration(
                        // Fond complètement transparent
                        color: Colors.transparent,
                      ),
                      // Ajouter un padding horizontal pour créer des marges latérales
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24.0), // Augmenter les marges latérales
                      // Ajouter un padding en bas pour remonter les boutons
                      margin: const EdgeInsets.only(
                          bottom: 12.0), // Augmenter la marge en bas
                      child: Theme(
                        // Utiliser un thème personnalisé pour la barre de navigation
                        data: Theme.of(context).copyWith(
                          navigationBarTheme: NavigationBarThemeData(
                            labelTextStyle:
                                WidgetStateProperty.resolveWith((states) {
                              // Style de texte pour les labels
                              if (states.contains(WidgetState.selected)) {
                                return TextStyle(
                                  fontSize: 12.0,
                                  fontWeight: FontWeight.w600,
                                  color: theme.colorScheme.primary,
                                );
                              }
                              return TextStyle(
                                fontSize: 12.0,
                                color: theme.colorScheme.onSurfaceVariant,
                              );
                            }),
                            iconTheme:
                                WidgetStateProperty.resolveWith((states) {
                              // Style d'icône
                              if (states.contains(WidgetState.selected)) {
                                return IconThemeData(
                                  size: 26.0,
                                  color: theme.colorScheme.primary,
                                );
                              }
                              return IconThemeData(
                                size: 26.0,
                                color: theme.colorScheme.onSurfaceVariant,
                              );
                            }),
                          ),
                        ),
                        child: NavigationBar(
                          selectedIndex: selectedIndex,
                          onDestinationSelected: _onDestinationSelected,
                          destinations: _destinations,
                          height: 70, // Hauteur de la barre
                          labelBehavior:
                              NavigationDestinationLabelBehavior.alwaysShow,
                          backgroundColor:
                              Colors.transparent, // Fond transparent
                          // Utiliser une couleur plus subtile pour l'indicateur
                          indicatorColor:
                              theme.colorScheme.primaryContainer.withValues(
                            red: theme.colorScheme.primaryContainer.r,
                            green: theme.colorScheme.primaryContainer.g,
                            blue: theme.colorScheme.primaryContainer.b,
                            alpha: 0.7,
                          ),
                          indicatorShape: RoundedRectangleBorder(
                            borderRadius:
                                BorderRadius.circular(12), // Augmenter le rayon
                          ),
                          // Personnaliser l'apparence des éléments
                          elevation: 0, // Pas d'ombre
                        ),
                      ),
                    ),
                  ],
                )
              : null, // Pas de barre de navigation pour les grands écrans
        ));
  }
}

class _AppSidebar extends ConsumerStatefulWidget {
  const _AppSidebar();

  @override
  ConsumerState<_AppSidebar> createState() => _AppSidebarState();
}

class _AppSidebarState extends ConsumerState<_AppSidebar> {
  bool _isNavigating = false;

  @override
  Widget build(BuildContext context) {
    final location = GoRouterState.of(context).uri.toString();
    final selectedIndex = _routeMap.entries
        .firstWhere((e) => location.startsWith(e.key),
            orElse: () => _routeMap.entries.first)
        .value;
    final sidebarState = ref.watch(sidebarProvider);

    // Fonction de navigation optimisée
    void navigateTo(int index) async {
      if (_isNavigating) return;

      setState(() {
        _isNavigating = true;
      });

      HapticFeedback.lightImpact();
      final newRoute =
          _routeMap.entries.firstWhere((e) => e.value == index).key;

      await Future.delayed(const Duration(milliseconds: 30));

      if (mounted) {
        context.go(newRoute);

        Future.delayed(const Duration(milliseconds: 200), () {
          if (mounted) {
            setState(() {
              _isNavigating = false;
            });
          }
        });
      }
    }

    // Raccourci clavier pour basculer l'état du menu (Ctrl+B)
    return Material(
      elevation: 1,
      color: Theme.of(context).colorScheme.surfaceContainer,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 250),
        width: sidebarState.isExpanded ? 260 : 72,
        curve: Curves.easeInOutCubic,
        child: Column(
          children: [
            // Entête du menu latéral
            Container(
              height: 56,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Center(
                child: sidebarState.isExpanded
                    ? const SeqqoLogo(fontSize: 24)
                    : const SeqqoFavicon(size: 28),
              ),
            ),
            const SizedBox(height: 16),

            // Section principale de navigation
            SidebarSection(
              title: "Navigation",
              isExpanded: sidebarState.isExpanded,
              items: [
                SidebarNavItem(
                  label: 'Tableau de bord',
                  icon: Icons.dashboard_outlined,
                  selectedIcon: Icons.dashboard,
                  onTap: () => navigateTo(0),
                  isSelected: selectedIndex == 0,
                ),
                SidebarNavItem(
                  label: 'Projets',
                  icon: Icons.folder_copy_outlined,
                  selectedIcon: Icons.folder_copy,
                  onTap: () => navigateTo(1),
                  isSelected: selectedIndex == 1,
                ),
                SidebarNavItem(
                  label: 'Clients',
                  icon: Icons.people_outline,
                  selectedIcon: Icons.people,
                  onTap: () => navigateTo(2),
                  isSelected: selectedIndex == 2,
                ),
                SidebarNavItem(
                  label: 'Annuaire',
                  icon: Icons.business_outlined,
                  selectedIcon: Icons.business,
                  onTap: () => navigateTo(3),
                  isSelected: selectedIndex == 3,
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Section secondaire (Outils, etc.)
            SidebarSection(
              title: "Outils",
              isExpanded: sidebarState.isExpanded,
              items: [
                SidebarNavItem(
                  label: 'Thème UI',
                  icon: Icons.palette_outlined,
                  selectedIcon: Icons.palette,
                  onTap: () => navigateTo(5),
                  isSelected: selectedIndex == 5,
                ),
              ],
            ),

            // Espace flexible pour pousser le reste en bas
            const Spacer(),

            // Séparateur
            Divider(
              height: 1,
              thickness: 1,
              color: Theme.of(context).colorScheme.outlineVariant,
              indent: sidebarState.isExpanded ? 24 : 12,
              endIndent: sidebarState.isExpanded ? 24 : 12,
            ),
            const SizedBox(height: 12),

            // Section de l'utilisateur
            const OrganizationSelector(),
            const SizedBox(height: 12),

            SidebarNavItem(
              label: 'Paramètres',
              icon: Icons.settings_outlined,
              selectedIcon: Icons.settings,
              onTap: () => navigateTo(4),
              isSelected: selectedIndex == 4,
            ),

            // Bouton pour réduire/agrandir le menu latéral
            const SidebarToggleButton(),
          ],
        ),
      ),
    );
  }
}
