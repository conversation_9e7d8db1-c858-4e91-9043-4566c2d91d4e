import 'package:flutter/material.dart';
import 'package:seqqo/core/utils/platform_utils.dart';
import 'package:seqqo/core/utils/responsive_utils.dart';

/// Toolbar mobile compact pour les écrans de liste
class MobileToolbar extends StatelessWidget {
  final VoidCallback? onSearchTap;
  final List<Widget> filterWidgets;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;

  const MobileToolbar({
    super.key,
    this.onSearchTap,
    this.filterWidgets = const [],
    this.padding,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(
        minHeight: 48, // Minimum touch target height
      ),
      padding: padding ??
          EdgeInsets.symmetric(
            horizontal: ResponsiveUtils.getHorizontalPadding(context),
            vertical: 12, // Increased padding for better touch targets
          ),
      decoration: BoxDecoration(
        color: backgroundColor ?? theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Bouton de recherche
          if (onSearchTap != null) ...[
            _SearchButton(onTap: onSearchTap!),
            const SizedBox(width: 12),
          ],

          // Widgets de filtrage
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              physics:
                  const BouncingScrollPhysics(), // Better mobile scroll physics
              child: Row(
                children: [
                  for (int i = 0; i < filterWidgets.length; i++) ...[
                    filterWidgets[i],
                    if (i < filterWidgets.length - 1)
                      const SizedBox(width: 12), // Increased spacing
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Bouton de recherche pour la toolbar mobile
class _SearchButton extends StatelessWidget {
  final VoidCallback onTap;

  const _SearchButton({required this.onTap});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Semantics(
      button: true,
      label: 'Ouvrir la recherche',
      hint: 'Appuyez pour ouvrir l\'interface de recherche',
      child: Material(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius:
            BorderRadius.circular(24), // Increased for better touch target
        child: InkWell(
          onTap: () {
            if (ResponsiveUtils.isMobile(context)) {
              PlatformUtils.triggerHapticFeedback(HapticFeedbackType.light);
            }
            onTap();
          },
          borderRadius: BorderRadius.circular(24),
          child: Container(
            constraints: const BoxConstraints(
              minHeight: 44, // iOS minimum touch target
              minWidth: 44, // Minimum touch target width
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.search,
                  size: 20, // Slightly larger for better visibility
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  semanticLabel: 'Icône de recherche',
                ),
                const SizedBox(width: 8),
                Text(
                  'Rechercher',
                  style: TextStyle(
                    fontSize: 15, // Slightly larger for better readability
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Widget de filtre compact pour la toolbar
class MobileFilterChip extends StatelessWidget {
  final String label;
  final IconData? icon;
  final bool isSelected;
  final VoidCallback onTap;
  final Color? selectedColor;

  const MobileFilterChip({
    super.key,
    required this.label,
    this.icon,
    this.isSelected = false,
    required this.onTap,
    this.selectedColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveColor = selectedColor ?? theme.colorScheme.primary;

    return Semantics(
      button: true,
      selected: isSelected,
      label: 'Filtre $label',
      hint: isSelected
          ? 'Filtre $label activé'
          : 'Appuyez pour activer le filtre $label',
      child: Material(
        color: isSelected
            ? effectiveColor.withValues(alpha: 0.12)
            : theme.colorScheme.surfaceContainerHighest,
        borderRadius:
            BorderRadius.circular(20), // Increased for better touch target
        child: InkWell(
          onTap: () {
            if (ResponsiveUtils.isMobile(context)) {
              PlatformUtils.triggerHapticFeedback(HapticFeedbackType.light);
            }
            onTap();
          },
          borderRadius: BorderRadius.circular(20),
          child: Container(
            constraints: const BoxConstraints(
              minHeight: 44, // Minimum touch target height
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (icon != null) ...[
                  Icon(
                    icon,
                    size: 18, // Slightly larger for better visibility
                    color: isSelected
                        ? effectiveColor
                        : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    semanticLabel: 'Icône $label',
                  ),
                  const SizedBox(width: 6),
                ],
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 14, // Increased for better readability
                    fontWeight: FontWeight.w500,
                    color: isSelected
                        ? effectiveColor
                        : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Dropdown compact pour la toolbar mobile
class MobileFilterDropdown<T> extends StatelessWidget {
  final String label;
  final IconData? icon;
  final T value;
  final List<DropdownMenuItem<T>> items;
  final ValueChanged<T?> onChanged;

  const MobileFilterDropdown({
    super.key,
    required this.label,
    this.icon,
    required this.value,
    required this.items,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Semantics(
      button: true,
      label: 'Filtre $label',
      hint: 'Appuyez pour changer le filtre $label',
      child: Container(
        constraints: const BoxConstraints(
          minHeight: 44, // Minimum touch target height
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest,
          borderRadius:
              BorderRadius.circular(20), // Increased for better touch target
        ),
        child: DropdownButtonHideUnderline(
          child: DropdownButton<T>(
            value: value,
            items: items,
            onChanged: (newValue) {
              if (ResponsiveUtils.isMobile(context)) {
                PlatformUtils.triggerHapticFeedback(HapticFeedbackType.light);
              }
              onChanged(newValue);
            },
            style: TextStyle(
              fontSize: 14, // Increased for better readability
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            icon: Icon(
              Icons.keyboard_arrow_down,
              size: 18, // Slightly larger for better visibility
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              semanticLabel: 'Ouvrir le menu $label',
            ),
            isDense: false, // Better spacing for touch targets
            dropdownColor: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }
}
