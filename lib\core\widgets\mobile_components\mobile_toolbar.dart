import 'package:flutter/material.dart';
import 'package:seqqo/core/utils/platform_utils.dart';
import 'package:seqqo/core/utils/responsive_utils.dart';
import 'package:seqqo/theme/app_theme.dart';

/// Toolbar mobile moderne pour les écrans de liste avec thème Seqqo
class MobileToolbar extends StatelessWidget {
  final VoidCallback? onSearchTap;
  final List<Widget> filterWidgets;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final int? activeFilterCount;
  final bool showFilterBadge;
  final VoidCallback? onRefresh;
  final String? title;
  final bool isLoading;

  const MobileToolbar({
    super.key,
    this.onSearchTap,
    this.filterWidgets = const [],
    this.padding,
    this.backgroundColor,
    this.activeFilterCount,
    this.showFilterBadge = false,
    this.onRefresh,
    this.title,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(
        minHeight: 56, // Seqqo standard height
      ),
      padding: padding ??
          EdgeInsets.symmetric(
            horizontal: ResponsiveUtils.getHorizontalPadding(context),
            vertical: 12,
          ),
      decoration: BoxDecoration(
        color: backgroundColor ?? theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.15),
            width: 1,
          ),
        ),
        // Subtle shadow for depth (Seqqo style)
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.04),
            offset: const Offset(0, 1),
            blurRadius: 2,
          ),
        ],
      ),
      child: Column(
        children: [
          // Main toolbar row
          Row(
            children: [
              // Title section (if provided)
              if (title != null) ...[
                Expanded(
                  child: Text(
                    title!,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 12),
              ],

              // Search button with modern design
              if (onSearchTap != null) ...[
                _ModernSearchButton(
                  onTap: onSearchTap!,
                  isLoading: isLoading,
                ),
                const SizedBox(width: 8),
              ],

              // Filter badge button
              if (showFilterBadge) ...[
                _FilterBadgeButton(
                  activeCount: activeFilterCount ?? 0,
                  onTap: () => _showFilterBottomSheet(context),
                ),
                const SizedBox(width: 8),
              ],

              // Refresh button
              if (onRefresh != null) ...[
                _RefreshButton(
                  onTap: onRefresh!,
                  isLoading: isLoading,
                ),
              ],
            ],
          ),

          // Filter chips row (if any)
          if (filterWidgets.isNotEmpty) ...[
            const SizedBox(height: 8),
            SizedBox(
              height: 32,
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                physics: const BouncingScrollPhysics(),
                itemCount: filterWidgets.length,
                separatorBuilder: (context, index) => const SizedBox(width: 8),
                itemBuilder: (context, index) => filterWidgets[index],
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _showFilterBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _FilterBottomSheet(
        filterWidgets: filterWidgets,
      ),
    );
  }
}

/// Bouton de recherche pour la toolbar mobile
class _SearchButton extends StatelessWidget {
  final VoidCallback onTap;

  const _SearchButton({required this.onTap});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Semantics(
      button: true,
      label: 'Ouvrir la recherche',
      hint: 'Appuyez pour ouvrir l\'interface de recherche',
      child: Material(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius:
            BorderRadius.circular(24), // Increased for better touch target
        child: InkWell(
          onTap: () {
            if (ResponsiveUtils.isMobile(context)) {
              PlatformUtils.triggerHapticFeedback(HapticFeedbackType.light);
            }
            onTap();
          },
          borderRadius: BorderRadius.circular(24),
          child: Container(
            constraints: const BoxConstraints(
              minHeight: 44, // iOS minimum touch target
              minWidth: 44, // Minimum touch target width
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.search,
                  size: 20, // Slightly larger for better visibility
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  semanticLabel: 'Icône de recherche',
                ),
                const SizedBox(width: 8),
                Text(
                  'Rechercher',
                  style: TextStyle(
                    fontSize: 15, // Slightly larger for better readability
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Widget de filtre compact pour la toolbar
class MobileFilterChip extends StatelessWidget {
  final String label;
  final IconData? icon;
  final bool isSelected;
  final VoidCallback onTap;
  final Color? selectedColor;

  const MobileFilterChip({
    super.key,
    required this.label,
    this.icon,
    this.isSelected = false,
    required this.onTap,
    this.selectedColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveColor = selectedColor ?? theme.colorScheme.primary;

    return Semantics(
      button: true,
      selected: isSelected,
      label: 'Filtre $label',
      hint: isSelected
          ? 'Filtre $label activé'
          : 'Appuyez pour activer le filtre $label',
      child: Material(
        color: isSelected
            ? effectiveColor.withValues(alpha: 0.12)
            : theme.colorScheme.surfaceContainerHighest,
        borderRadius:
            BorderRadius.circular(20), // Increased for better touch target
        child: InkWell(
          onTap: () {
            if (ResponsiveUtils.isMobile(context)) {
              PlatformUtils.triggerHapticFeedback(HapticFeedbackType.light);
            }
            onTap();
          },
          borderRadius: BorderRadius.circular(20),
          child: Container(
            constraints: const BoxConstraints(
              minHeight: 44, // Minimum touch target height
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (icon != null) ...[
                  Icon(
                    icon,
                    size: 18, // Slightly larger for better visibility
                    color: isSelected
                        ? effectiveColor
                        : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    semanticLabel: 'Icône $label',
                  ),
                  const SizedBox(width: 6),
                ],
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 14, // Increased for better readability
                    fontWeight: FontWeight.w500,
                    color: isSelected
                        ? effectiveColor
                        : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Dropdown compact pour la toolbar mobile
class MobileFilterDropdown<T> extends StatelessWidget {
  final String label;
  final IconData? icon;
  final T value;
  final List<DropdownMenuItem<T>> items;
  final ValueChanged<T?> onChanged;

  const MobileFilterDropdown({
    super.key,
    required this.label,
    this.icon,
    required this.value,
    required this.items,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Semantics(
      button: true,
      label: 'Filtre $label',
      hint: 'Appuyez pour changer le filtre $label',
      child: Container(
        constraints: const BoxConstraints(
          minHeight: 44, // Minimum touch target height
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: theme.colorScheme.surfaceContainerHighest,
          borderRadius:
              BorderRadius.circular(20), // Increased for better touch target
        ),
        child: DropdownButtonHideUnderline(
          child: DropdownButton<T>(
            value: value,
            items: items,
            onChanged: (newValue) {
              if (ResponsiveUtils.isMobile(context)) {
                PlatformUtils.triggerHapticFeedback(HapticFeedbackType.light);
              }
              onChanged(newValue);
            },
            style: TextStyle(
              fontSize: 14, // Increased for better readability
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            icon: Icon(
              Icons.keyboard_arrow_down,
              size: 18, // Slightly larger for better visibility
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              semanticLabel: 'Ouvrir le menu $label',
            ),
            isDense: false, // Better spacing for touch targets
            dropdownColor: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }
}

/// Bouton de recherche moderne avec design Seqqo
class _ModernSearchButton extends StatelessWidget {
  final VoidCallback onTap;
  final bool isLoading;

  const _ModernSearchButton({
    required this.onTap,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Semantics(
      button: true,
      label: 'Ouvrir la recherche',
      hint: 'Appuyez pour ouvrir l\'interface de recherche',
      child: Material(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8), // Seqqo border radius
        elevation: 0,
        child: InkWell(
          onTap: isLoading
              ? null
              : () {
                  if (ResponsiveUtils.isMobile(context)) {
                    PlatformUtils.triggerHapticFeedback(
                        HapticFeedbackType.light);
                  }
                  onTap();
                },
          borderRadius: BorderRadius.circular(8),
          child: Container(
            constraints: const BoxConstraints(
              minHeight: 44,
              minWidth: 44,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.search_rounded,
                  size: 18,
                  color: isLoading
                      ? theme.colorScheme.onSurface.withValues(alpha: 0.4)
                      : theme.colorScheme.primary,
                  semanticLabel: 'Icône de recherche',
                ),
                const SizedBox(width: 6),
                Text(
                  'Rechercher',
                  style: TextStyle(
                    fontSize: 14,
                    color: isLoading
                        ? theme.colorScheme.onSurface.withValues(alpha: 0.4)
                        : theme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Bouton de badge de filtres avec compteur
class _FilterBadgeButton extends StatelessWidget {
  final int activeCount;
  final VoidCallback onTap;

  const _FilterBadgeButton({
    required this.activeCount,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Semantics(
      button: true,
      label: 'Filtres actifs: $activeCount',
      hint: 'Appuyez pour gérer les filtres',
      child: Material(
        color: activeCount > 0
            ? theme.colorScheme.primary.withValues(alpha: 0.1)
            : theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          onTap: () {
            if (ResponsiveUtils.isMobile(context)) {
              PlatformUtils.triggerHapticFeedback(HapticFeedbackType.light);
            }
            onTap();
          },
          borderRadius: BorderRadius.circular(8),
          child: Container(
            constraints: const BoxConstraints(
              minHeight: 44,
              minWidth: 44,
            ),
            padding: const EdgeInsets.all(10),
            child: Stack(
              children: [
                Icon(
                  Icons.tune_rounded,
                  size: 18,
                  color: activeCount > 0
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                if (activeCount > 0)
                  Positioned(
                    right: -2,
                    top: -2,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.error,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 12,
                        minHeight: 12,
                      ),
                      child: Text(
                        activeCount > 9 ? '9+' : activeCount.toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 8,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Bouton de rafraîchissement avec animation
class _RefreshButton extends StatefulWidget {
  final VoidCallback onTap;
  final bool isLoading;

  const _RefreshButton({
    required this.onTap,
    this.isLoading = false,
  });

  @override
  State<_RefreshButton> createState() => _RefreshButtonState();
}

class _RefreshButtonState extends State<_RefreshButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
  }

  @override
  void didUpdateWidget(_RefreshButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isLoading && !oldWidget.isLoading) {
      _animationController.repeat();
    } else if (!widget.isLoading && oldWidget.isLoading) {
      _animationController.stop();
      _animationController.reset();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Semantics(
      button: true,
      label: 'Actualiser',
      hint: 'Appuyez pour actualiser la liste',
      child: Material(
        color: theme.colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
        child: InkWell(
          onTap: widget.isLoading
              ? null
              : () {
                  if (ResponsiveUtils.isMobile(context)) {
                    PlatformUtils.triggerHapticFeedback(
                        HapticFeedbackType.light);
                  }
                  widget.onTap();
                },
          borderRadius: BorderRadius.circular(8),
          child: Container(
            constraints: const BoxConstraints(
              minHeight: 44,
              minWidth: 44,
            ),
            padding: const EdgeInsets.all(10),
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _animationController.value * 2 * 3.14159,
                  child: Icon(
                    Icons.refresh_rounded,
                    size: 18,
                    color: widget.isLoading
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

/// Bottom sheet moderne pour les filtres
class _FilterBottomSheet extends StatelessWidget {
  final List<Widget> filterWidgets;

  const _FilterBottomSheet({
    required this.filterWidgets,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            offset: const Offset(0, -2),
            blurRadius: 8,
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: theme.colorScheme.outline.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Icon(
                    Icons.tune_rounded,
                    color: theme.colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Filtres',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close_rounded,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                    tooltip: 'Fermer',
                  ),
                ],
              ),
            ),

            // Filter content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Wrap(
                  spacing: 12,
                  runSpacing: 12,
                  children: filterWidgets,
                ),
              ),
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
