import 'package:flutter/material.dart';
import 'package:seqqo/features/clients/models/client_data.dart';
import 'package:intl/intl.dart';
import 'package:seqqo/features/clients/utils/enseigne_utils.dart';
import 'package:seqqo/core/utils/responsive_utils.dart';
import 'package:seqqo/core/widgets/mobile_components/swipe_action_card.dart';
import 'package:seqqo/core/utils/platform_utils.dart';
import 'package:url_launcher/url_launcher.dart';

class ClientListWidget extends StatefulWidget {
  final List<ClientData> clients;
  final ClientData? selectedClient;
  final bool isLoading;
  final ValueChanged<ClientData> onClientSelected;
  final ValueChanged<ClientData> onDeleteClient;
  final ValueChanged<ClientData>? onOpenFullPage;

  const ClientListWidget({
    super.key,
    required this.clients,
    required this.selectedClient,
    required this.isLoading,
    required this.onClientSelected,
    required this.onDeleteClient,
    this.onOpenFullPage,
  });

  @override
  State<ClientListWidget> createState() => _ClientListWidgetState();
}

class _ClientListWidgetState extends State<ClientListWidget> {
  int _sortColumnIndex = 0;
  bool _sortAscending = false;
  final DateFormat _dateFormat = DateFormat('dd/MM/yyyy');
  final ScrollController _scrollController = ScrollController();

  // Méthode pour extraire les initiales d'un nom
  String _getInitials(String? name) {
    if (name == null || name.trim().isEmpty) {
      return '?';
    }

    List<String> words =
        name.trim().split(' ').where((s) => s.isNotEmpty).toList();
    if (words.isEmpty) {
      return '?';
    } else if (words.length == 1) {
      return words[0][0].toUpperCase();
    } else {
      return (words[0][0] + words[words.length > 1 ? 1 : 0][0]).toUpperCase();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isMobile = ResponsiveUtils.isMobile(context);

    if (widget.isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'Chargement des clients...',
              style: theme.textTheme.bodyLarge,
            ),
          ],
        ),
      );
    }

    if (widget.clients.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: theme.colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              'Aucun client trouvé',
              style: theme.textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              'Utilisez le bouton + pour en ajouter un',
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    // Afficher la liste des clients selon le type d'écran
    return isMobile ? _buildMobileList(theme) : _buildDesktopTable(theme);
  }

  // Liste mobile optimisée
  Widget _buildMobileList(ThemeData theme) {
    return ListView.builder(
      controller: _scrollController,
      itemCount: widget.clients.length,
      padding: const EdgeInsets.symmetric(vertical: 8),
      physics: const BouncingScrollPhysics(), // Better mobile scroll physics
      itemBuilder: (context, index) {
        final client = widget.clients[index];
        final isSelected = widget.selectedClient?.clientId == client.clientId;

        return _buildMobileClientCard(context, client, isSelected, theme);
      },
    );
  }

  // Tableau desktop
  Widget _buildDesktopTable(ThemeData theme) {
    return Column(
      children: [
        // En-tête de tableau fixe
        Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            border: Border(
              bottom: BorderSide(
                color: theme.colorScheme.outline.withAlpha(30),
                width: 1,
              ),
            ),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          child: Row(
            children: _buildTableHeaders(theme),
          ),
        ),

        // Corps du tableau avec défilement
        Expanded(
          child: ListView.builder(
            controller: _scrollController,
            itemCount: widget.clients.length,
            padding: EdgeInsets.zero,
            itemBuilder: (context, index) {
              final client = widget.clients[index];
              final isSelected =
                  widget.selectedClient?.clientId == client.clientId;

              return _buildClientRow(context, client, isSelected, theme);
            },
          ),
        ),
      ],
    );
  }

  // Carte client mobile optimisée avec actions de swipe
  Widget _buildMobileClientCard(BuildContext context, ClientData client,
      bool isSelected, ThemeData theme) {
    return SwipeActionCard(
      longPressTitle: 'Actions pour ${client.name ?? "ce client"}',
      leftActions: [
        // Action d'appel (si numéro disponible)
        if (client.mainContact?.telephone != null &&
            client.mainContact!.telephone!.isNotEmpty)
          SwipeAction.call(
            onPressed: () => _callClient(client),
          ),
        // Action d'email (si email disponible)
        if (client.email != null && client.email!.isNotEmpty)
          SwipeAction.email(
            onPressed: () => _emailClient(client),
          ),
      ],
      rightActions: [
        // Action de modification
        SwipeAction.edit(
          onPressed: () {
            if (widget.onOpenFullPage != null) {
              widget.onOpenFullPage!(client);
            }
          },
        ),
        // Action de suppression
        SwipeAction.delete(
          onPressed: () => _confirmDelete(context, client),
        ),
      ],
      child: Semantics(
        button: true,
        label: 'Client ${client.name ?? "sans nom"}',
        hint:
            'Appuyez pour voir les détails du client, glissez pour les actions',
        selected: isSelected,
        child: Card(
          elevation: 1,
          margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12), // Seqqo border radius
            side: BorderSide(
              color: isSelected
                  ? theme.colorScheme.primary.withAlpha(100)
                  : theme.colorScheme.outline.withAlpha(40),
              width: 1,
            ),
          ),
          color: isSelected
              ? theme.colorScheme.primary.withAlpha(15)
              : theme.colorScheme.surface,
          child: InkWell(
            onTap: () => widget.onClientSelected(client),
            borderRadius: BorderRadius.circular(12),
            hoverColor: theme.colorScheme.primary.withAlpha(10),
            child: Padding(
              padding: const EdgeInsets.all(16.0), // Augmenté pour mobile
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Première ligne: Nom du client et statut (style projets)
                  Row(
                    children: [
                      Icon(
                        Icons.business,
                        size: 14,
                        color: isSelected
                            ? theme.colorScheme.primary
                            : theme.colorScheme.onSurface.withAlpha(150),
                      ),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          client.name ?? 'Sans nom',
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight:
                                isSelected ? FontWeight.w600 : FontWeight.w500,
                            color: isSelected
                                ? theme.colorScheme.primary
                                : theme.colorScheme.onSurface,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 6),
                      _buildStatutChip(client.statut),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Deuxième ligne: Type d'enseigne
                  Row(
                    children: [
                      Icon(
                        Icons.store_outlined,
                        size: 14,
                        color: theme.colorScheme.onSurface.withAlpha(120),
                      ),
                      const SizedBox(width: 6),
                      Expanded(
                        child: client.enseigneType != null &&
                                client.enseigneType!.isNotEmpty
                            ? Text(
                                client.enseigneType!,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: theme.colorScheme.onSurface
                                      .withAlpha(180),
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              )
                            : Text(
                                'Type non défini',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontStyle: FontStyle.italic,
                                  color: theme.colorScheme.onSurface
                                      .withAlpha(120),
                                ),
                              ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Troisième ligne: Contact
                  if (client.mainContact?.interlocuteur != null)
                    Row(
                      children: [
                        Icon(
                          Icons.person_outline,
                          size: 14,
                          color: theme.colorScheme.onSurface.withAlpha(120),
                        ),
                        const SizedBox(width: 6),
                        Expanded(
                          child: Text(
                            client.mainContact!.interlocuteur!,
                            style: TextStyle(
                              fontSize: 12,
                              color: theme.colorScheme.onSurface.withAlpha(180),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),

                  const SizedBox(height: 8),

                  // Quatrième ligne: Date et indicateur de swipe (style projets)
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_today_outlined,
                        size: 14,
                        color: theme.colorScheme.onSurface.withAlpha(120),
                      ),
                      const SizedBox(width: 6),
                      Text(
                        client.createdAt != null
                            ? _dateFormat.format(client.createdAt!.toDate())
                            : 'N/A',
                        style: TextStyle(
                          fontSize: 11,
                          color: theme.colorScheme.onSurface.withAlpha(150),
                        ),
                      ),
                      const Spacer(),
                      // Indicateur de swipe sur mobile
                      Icon(
                        Icons.more_horiz,
                        size: 16,
                        color: theme.colorScheme.onSurface.withAlpha(120),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Construit les en-têtes de colonnes du tableau
  List<Widget> _buildTableHeaders(ThemeData theme) {
    return [
      // Nom
      Expanded(
        flex: 3,
        child: _buildColumnHeader(
          'Nom',
          Icons.business,
          theme,
          onSort: () {
            setState(() {
              _sortColumnIndex = 0;
              _sortAscending = !_sortAscending;
              _sort<String>((c) => c.name ?? '', 0, _sortAscending);
            });
          },
          isSorted: _sortColumnIndex == 0,
          sortDirection: _sortAscending,
          tooltip: 'Trier par nom de client',
        ),
      ),
      _buildVerticalDivider(theme),
      // Type d'enseigne
      Expanded(
        flex: 2,
        child: _buildColumnHeader(
          'Type d\'enseigne',
          Icons.store,
          theme,
          onSort: () {
            setState(() {
              _sortColumnIndex = 1;
              _sortAscending = !_sortAscending;
              _sort<String>((c) => c.enseigneType ?? '', 1, _sortAscending);
            });
          },
          isSorted: _sortColumnIndex == 1,
          sortDirection: _sortAscending,
          tooltip: 'Trier par type d\'enseigne',
        ),
      ),
      _buildVerticalDivider(theme),
      // Contact
      Expanded(
        flex: 2,
        child: _buildColumnHeader(
          'Contact',
          Icons.person,
          theme,
          onSort: () {
            setState(() {
              _sortColumnIndex = 2;
              _sortAscending = !_sortAscending;
              _sort<String>(
                  (c) => c.mainContact?.interlocuteur ?? '', 2, _sortAscending);
            });
          },
          isSorted: _sortColumnIndex == 2,
          sortDirection: _sortAscending,
          tooltip: 'Trier par contact',
        ),
      ),
      _buildVerticalDivider(theme),
      // Statut
      Expanded(
        flex: 1,
        child: _buildColumnHeader(
          'Statut',
          Icons.info_outline,
          theme,
          onSort: () {
            setState(() {
              _sortColumnIndex = 3;
              _sortAscending = !_sortAscending;
              _sort<String>((c) => c.statut ?? '', 3, _sortAscending);
            });
          },
          isSorted: _sortColumnIndex == 3,
          sortDirection: _sortAscending,
          tooltip: 'Trier par statut',
        ),
      ),
      _buildVerticalDivider(theme),
      // Date
      Expanded(
        flex: 1,
        child: _buildColumnHeader(
          'Date',
          Icons.calendar_today,
          theme,
          onSort: () {
            setState(() {
              _sortColumnIndex = 4;
              _sortAscending = !_sortAscending;
              _sort<DateTime>((c) => c.createdAt?.toDate() ?? DateTime(0), 4,
                  _sortAscending);
            });
          },
          isSorted: _sortColumnIndex == 4,
          sortDirection: _sortAscending,
          tooltip: 'Trier par date de création',
        ),
      ),
      _buildVerticalDivider(theme),
      // Actions (largeur fixe)
      SizedBox(
        width: 100,
        child: _buildColumnHeader(
          'Actions',
          Icons.more_horiz,
          theme,
          showSortIcon: false,
          tooltip: 'Actions disponibles',
        ),
      ),
    ];
  }

  // Construit un séparateur vertical entre les colonnes
  Widget _buildVerticalDivider(ThemeData theme) {
    return Container(
      width: 1,
      height: 24,
      color: theme.colorScheme.outline.withAlpha(30),
      margin: const EdgeInsets.symmetric(horizontal: 4),
    );
  }

  // Construit un en-tête de colonne
  Widget _buildColumnHeader(
    String title,
    IconData icon,
    ThemeData theme, {
    VoidCallback? onSort,
    bool isSorted = false,
    bool sortDirection = true,
    String? tooltip,
    bool showSortIcon = true,
  }) {
    return Tooltip(
      message: tooltip ?? title,
      child: InkWell(
        onTap: onSort,
        borderRadius: BorderRadius.circular(4),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
          child: Row(
            children: [
              Icon(
                icon,
                size: 14,
                color: isSorted
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface.withAlpha(150),
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: isSorted
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface.withAlpha(180),
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (showSortIcon && onSort != null) ...[
                const SizedBox(width: 4),
                Icon(
                  isSorted
                      ? (sortDirection
                          ? Icons.arrow_upward
                          : Icons.arrow_downward)
                      : Icons.unfold_more,
                  size: 14,
                  color: isSorted
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurface.withAlpha(100),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // Construit une ligne de client
  Widget _buildClientRow(
    BuildContext context,
    ClientData client,
    bool isSelected,
    ThemeData theme,
  ) {
    return Material(
      color: isSelected
          ? theme.colorScheme.primary.withAlpha(15)
          : theme.colorScheme.surface,
      child: InkWell(
        onTap: () => widget.onClientSelected(client),
        hoverColor: theme.colorScheme.primary.withAlpha(10),
        child: Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: theme.colorScheme.outline.withAlpha(15),
                width: 1,
              ),
            ),
          ),
          child: Padding(
            padding:
                const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
            child: Row(
              children: [
                // Nom
                Expanded(
                  flex: 3,
                  child: Row(
                    children: [
                      _buildClientLogo(context, client),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          client.name ?? 'Sans nom',
                          style: TextStyle(
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal,
                            color: isSelected
                                ? theme.colorScheme.primary
                                : theme.colorScheme.onSurface,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildVerticalDivider(theme),
                // Type d'enseigne
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: client.enseigneType != null &&
                            client.enseigneType!.isNotEmpty
                        ? Row(
                            children: [
                              Container(
                                width: 8,
                                height: 8,
                                decoration: BoxDecoration(
                                  color: getColorForEnseigneType(
                                      client.enseigneType),
                                  borderRadius: BorderRadius.circular(2),
                                ),
                              ),
                              const SizedBox(width: 6),
                              Expanded(
                                child: Text(
                                  client.enseigneType ?? 'Non défini',
                                  style: TextStyle(
                                    color: theme.colorScheme.onSurface,
                                    fontSize: 13,
                                    fontWeight: FontWeight.w400,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          )
                        : Text(
                            'Non défini',
                            style: TextStyle(
                              color: theme.colorScheme.onSurface.withAlpha(150),
                              fontStyle: FontStyle.italic,
                              fontSize: 13,
                            ),
                          ),
                  ),
                ),
                _buildVerticalDivider(theme),
                // Contact
                Expanded(
                  flex: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Text(
                      client.mainContact?.interlocuteur ?? 'Non défini',
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        color: client.mainContact?.interlocuteur != null
                            ? theme.colorScheme.onSurface
                            : theme.colorScheme.onSurface.withAlpha(150),
                        fontStyle: client.mainContact?.interlocuteur != null
                            ? FontStyle.normal
                            : FontStyle.italic,
                      ),
                    ),
                  ),
                ),
                _buildVerticalDivider(theme),
                // Statut
                Expanded(
                  flex: 1,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: _buildStatutChip(client.statut),
                  ),
                ),
                _buildVerticalDivider(theme),
                // Date
                Expanded(
                  flex: 1,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Text(
                      client.createdAt != null
                          ? _dateFormat.format(client.createdAt!.toDate())
                          : 'N/A',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withAlpha(180),
                      ),
                    ),
                  ),
                ),
                _buildVerticalDivider(theme),
                // Actions
                SizedBox(
                  width: 100,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (widget.onOpenFullPage != null)
                        IconButton(
                          icon: Icon(
                            Icons.edit_outlined,
                            size: 18,
                            color: theme.colorScheme.primary,
                          ),
                          tooltip: 'Modifier',
                          visualDensity: VisualDensity.compact,
                          onPressed: () => widget.onOpenFullPage!(client),
                        ),
                      IconButton(
                        icon: Icon(
                          Icons.delete_outline,
                          size: 18,
                          color: theme.colorScheme.error,
                        ),
                        tooltip: 'Supprimer',
                        visualDensity: VisualDensity.compact,
                        onPressed: () => _confirmDelete(context, client),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _sort<T>(Comparable<T> Function(ClientData c) getField, int columnIndex,
      bool ascending) {
    setState(() {
      _sortColumnIndex = columnIndex;
      _sortAscending = ascending;

      widget.clients.sort((a, b) {
        final aValue = getField(a);
        final bValue = getField(b);

        return ascending
            ? Comparable.compare(aValue, bValue)
            : Comparable.compare(bValue, aValue);
      });
    });
  }

  Future<void> _confirmDelete(BuildContext context, ClientData client) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmation'),
        content: Text(
            'Êtes-vous sûr de vouloir supprimer le client "${client.name}" ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Supprimer'),
          ),
        ],
      ),
    );

    if (result == true) {
      widget.onDeleteClient(client);
    }
  }

  // Widget pour afficher le statut du client de façon cohérente avec les projets
  Widget _buildStatutChip(String? statut) {
    Color color;
    IconData icon;
    String label = statut ?? 'Actif';

    switch (label) {
      case 'Actif':
        color = Colors.green.shade600;
        icon = Icons.check_circle;
        break;
      case 'Prospect':
        color = Colors.blue.shade600;
        icon = Icons.lightbulb;
        break;
      case 'Inactif':
        color = Colors.orange.shade600;
        icon = Icons.pause_circle;
        break;
      case 'Archivé':
        color = Colors.brown.shade400;
        icon = Icons.archive;
        break;
      case 'Blacklisté':
        color = Colors.red.shade600;
        icon = Icons.block;
        break;
      default:
        color = Colors.teal.shade600;
        icon = Icons.help_outline;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        color: color.withAlpha(20),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withAlpha(60), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 12,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Widget pour afficher l'avatar du client avec ses initiales (style cohérent)
  Widget _buildClientLogo(BuildContext context, ClientData client) {
    final theme = Theme.of(context);
    final initials = _getInitials(client.name);

    // Couleur basée sur le hash du nom pour cohérence
    final colorIndex = (client.name?.hashCode ?? 0).abs() % 6;
    final colors = [
      theme.colorScheme.primary,
      Colors.blue.shade600,
      Colors.green.shade600,
      Colors.orange.shade600,
      Colors.purple.shade600,
      Colors.teal.shade600,
    ];
    final backgroundColor = colors[colorIndex];

    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: backgroundColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: backgroundColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Center(
        child: Text(
          initials,
          style: TextStyle(
            color: backgroundColor,
            fontWeight: FontWeight.w700,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  // Actions de swipe pour mobile
  void _callClient(ClientData client) {
    final phone = client.mainContact?.telephone;
    if (phone != null && phone.isNotEmpty) {
      PlatformUtils.triggerHapticFeedback(HapticFeedbackType.medium);
      _launchUrl('tel:$phone');
    }
  }

  void _emailClient(ClientData client) {
    final email = client.email;
    if (email != null && email.isNotEmpty) {
      PlatformUtils.triggerHapticFeedback(HapticFeedbackType.medium);
      _launchUrl('mailto:$email');
    }
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      }
    } catch (e) {
      // Gérer l'erreur silencieusement ou afficher un message
      debugPrint('Erreur lors du lancement de l\'URL: $e');
    }
  }
}
