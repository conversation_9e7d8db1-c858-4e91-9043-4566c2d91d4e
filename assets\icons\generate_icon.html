<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur d'icône Seqqo</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        
        .icon-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }
        
        .icon {
            width: 512px;
            height: 512px;
            background: #2D2D2D;
            border-radius: 128px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        
        .s-letter {
            font-family: 'Arial Black', Arial, sans-serif;
            font-size: 280px;
            font-weight: 900;
            color: white;
            line-height: 1;
            text-align: center;
            letter-spacing: -10px;
        }
        
        .instructions {
            max-width: 600px;
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        
        .download-btn {
            background: #2D2D2D;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 10px;
        }
        
        .download-btn:hover {
            background: #404040;
        }
        
        .sizes {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 20px;
        }
        
        .size-icon {
            background: #2D2D2D;
            border-radius: 25%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .size-16 { width: 16px; height: 16px; font-size: 8px; }
        .size-32 { width: 32px; height: 32px; font-size: 16px; }
        .size-48 { width: 48px; height: 48px; font-size: 24px; }
        .size-72 { width: 72px; height: 72px; font-size: 36px; }
        .size-96 { width: 96px; height: 96px; font-size: 48px; }
        .size-144 { width: 144px; height: 144px; font-size: 72px; }
        .size-192 { width: 192px; height: 192px; font-size: 96px; }
    </style>
</head>
<body>
    <div class="icon-container">
        <div class="icon" id="mainIcon">
            <div class="s-letter">S</div>
        </div>
        
        <div class="instructions">
            <h2>🎨 Icône Seqqo</h2>
            <p>Ceci est l'icône principale de l'application Seqqo.</p>
            <p><strong>Instructions :</strong></p>
            <ol style="text-align: left;">
                <li>Faites un clic droit sur l'icône ci-dessus</li>
                <li>Sélectionnez "Enregistrer l'image sous..."</li>
                <li>Sauvegardez comme "seqqo_icon.png"</li>
                <li>Placez le fichier dans le dossier "assets/icons/"</li>
            </ol>
            <button class="download-btn" onclick="downloadIcon()">📥 Télécharger l'icône</button>
        </div>
        
        <div class="instructions">
            <h3>Aperçu des tailles</h3>
            <div class="sizes">
                <div class="size-icon size-16">S</div>
                <div class="size-icon size-32">S</div>
                <div class="size-icon size-48">S</div>
                <div class="size-icon size-72">S</div>
                <div class="size-icon size-96">S</div>
                <div class="size-icon size-144">S</div>
                <div class="size-icon size-192">S</div>
            </div>
            <p><small>Aperçu des différentes tailles d'icônes</small></p>
        </div>
    </div>

    <script>
        function downloadIcon() {
            // Créer un canvas pour dessiner l'icône
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = 512;
            canvas.height = 512;
            
            // Dessiner le fond avec coins arrondis
            ctx.fillStyle = '#2D2D2D';
            ctx.beginPath();
            ctx.roundRect(0, 0, 512, 512, 128);
            ctx.fill();
            
            // Dessiner la lettre S
            ctx.fillStyle = 'white';
            ctx.font = 'bold 280px Arial Black, Arial, sans-serif';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('S', 256, 256);
            
            // Télécharger l'image
            const link = document.createElement('a');
            link.download = 'seqqo_icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Polyfill pour roundRect si nécessaire
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
